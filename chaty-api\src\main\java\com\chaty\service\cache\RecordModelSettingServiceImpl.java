package com.chaty.service.cache;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.RecordModelSettingEntity;
import com.chaty.mapper.RecordModelSettingMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 缓存策略：
 * - Hash：RMS:FILE:{fileId}     field=questionType   value=JSON(RecordModelSettingEntity)
 * - Hash：RMS:CFG:{configId}    field=questionType   value=JSON(RecordModelSettingEntity)
 *
 * 典型定位键：
 * - fileId + questionType
 * - configPackageId + questionType
 *
 * 写（add/update/delete）后同步刷新对应 Hash 的 field。
 * 启动：全量扫描 DB，删除旧 key，重建所有缓存。
 */
@Slf4j
@Service
public class RecordModelSettingServiceImpl
        extends ServiceImpl<RecordModelSettingMapper, RecordModelSettingEntity>
        implements RecordModelSettingService {

    private static final String KEY_FILE_PREFIX = "RMS:FILE:"; // + fileId
    private static final String KEY_CFG_PREFIX  = "RMS:CFG:";  // + configPackageId

    @Resource
    private RecordModelSettingMapper recordModelSettingMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ObjectMapper objectMapper;



    // ===================== 启动预热 =====================
    @PostConstruct
    public void initCache() {
        try {
            rebuildAllCaches();
            log.info("[RecordModelSetting] Cache warmed on startup.");
        } catch (Exception e) {
            log.warn("[RecordModelSetting] Cache warm-up failed: {}", e.getMessage());
        }
    }

    /**
     * 全量重建所有 Redis 缓存：
     * 1) 删除现有 RMS:FILE:*、RMS:CFG:* key
     * 2) 扫描 DB 全表
     * 3) 按 fileId / configPackageId 分组，批量 putAll 写入
     */
    private void rebuildAllCaches() {
        // 1) 清理旧 key（注意：keys 操作在大规模 key 场景下成本较高；本场景量小可接受）
        try {
            Set<String> fileKeys = stringRedisTemplate.keys(KEY_FILE_PREFIX + "*");
            Set<String> cfgKeys  = stringRedisTemplate.keys(KEY_CFG_PREFIX + "*");
            if (fileKeys != null && !fileKeys.isEmpty()) {
                stringRedisTemplate.delete(fileKeys);
            }
            if (cfgKeys != null && !cfgKeys.isEmpty()) {
                stringRedisTemplate.delete(cfgKeys);
            }
        } catch (Exception e) {
            log.warn("Redis 清理旧 key 失败：{}", e.getMessage());
        }

        // 2) 扫描 DB 全表
        List<RecordModelSettingEntity> all = this.list();
        if (all == null || all.isEmpty()) {
            return;
        }

        // 3) 按 fileId / configPackageId 分组后批量写入
        Map<String, List<RecordModelSettingEntity>> byFile =
                all.stream()
                        .filter(e -> StrUtil.isNotBlank(e.getFileId()) && StrUtil.isNotBlank(e.getQuestionType()))
                        .collect(Collectors.groupingBy(RecordModelSettingEntity::getFileId));

        Map<String, List<RecordModelSettingEntity>> byCfg =
                all.stream()
                        .filter(e -> StrUtil.isNotBlank(e.getConfigPackageId()) && StrUtil.isNotBlank(e.getQuestionType()))
                        .collect(Collectors.groupingBy(RecordModelSettingEntity::getConfigPackageId));

        // file 维度
        for (Map.Entry<String, List<RecordModelSettingEntity>> entry : byFile.entrySet()) {
            String fileId = entry.getKey();
            Map<String, String> map = new HashMap<>();
            for (RecordModelSettingEntity e : entry.getValue()) {
                try {
                    map.put(e.getQuestionType(), objectMapper.writeValueAsString(e));
                } catch (Exception ignore) {}
            }
            if (!map.isEmpty()) {
                try {
                    stringRedisTemplate.opsForHash().putAll(KEY_FILE_PREFIX + fileId, map);
                } catch (DataAccessException ex) {
                    log.warn("Redis 预热 FILE({}) 失败：{}", fileId, ex.getMessage());
                }
            }
        }

        // config 维度
        for (Map.Entry<String, List<RecordModelSettingEntity>> entry : byCfg.entrySet()) {
            String cfgId = entry.getKey();
            Map<String, String> map = new HashMap<>();
            for (RecordModelSettingEntity e : entry.getValue()) {
                try {
                    map.put(e.getQuestionType(), objectMapper.writeValueAsString(e));
                } catch (Exception ignore) {}
            }
            if (!map.isEmpty()) {
                try {
                    stringRedisTemplate.opsForHash().putAll(KEY_CFG_PREFIX + cfgId, map);
                } catch (DataAccessException ex) {
                    log.warn("Redis 预热 CFG({}) 失败：{}", cfgId, ex.getMessage());
                }
            }
        }
    }

    // ===================== Page（走 DB） =====================

    @Override
    public IPage<RecordModelSettingEntity> page(Integer pageNumber, Integer pageSize,
                                                String type, String fileId, String configPackageId,
                                                String questionType) {
        Page<RecordModelSettingEntity> page = new Page<>(
                pageNumber == null ? 1 : pageNumber,
                pageSize == null ? 10 : pageSize
        );

        LambdaQueryWrapper<RecordModelSettingEntity> qw = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(type)) qw.eq(RecordModelSettingEntity::getType, type);
        if (StringUtils.hasText(fileId)) qw.eq(RecordModelSettingEntity::getFileId, fileId);
        if (StringUtils.hasText(configPackageId)) qw.eq(RecordModelSettingEntity::getConfigPackageId, configPackageId);
        if (StringUtils.hasText(questionType)) qw.eq(RecordModelSettingEntity::getQuestionType, questionType);
        qw.orderByDesc(RecordModelSettingEntity::getCreateTime);

        return this.page(page, qw);
    }

    // ===================== 写操作：DB + Cache =====================

    @Override
    public Long add(RecordModelSettingEntity param) {
        // 兜底：若未填 type，根据是否有 fileId / configPackageId 自动判定
        if (!StringUtils.hasText(param.getType())) {
            if (StringUtils.hasText(param.getFileId())) {
                param.setType("file");
            } else if (StringUtils.hasText(param.getConfigPackageId())) {
                param.setType("config");
            }
        }
        this.save(param);
        // 从库读取最新对象，回填缓存
        RecordModelSettingEntity db = this.getById(param.getId());
        putCache(db);
        return param.getId();
    }

    @Override
    public Long updateOne(RecordModelSettingEntity param) {
        this.updateById(param);
        // 为避免部分字段未传导致缓存不完整，统一从库查询最新记录后写缓存
        RecordModelSettingEntity db = this.getById(param.getId());
        putCache(db);
        return param.getId();
    }

    @Override
    public void deleteById(Long id) {
        if (id == null) return;
        // 先查出旧值，便于删除缓存
        RecordModelSettingEntity old = this.getById(id);
        this.removeById(id);
        if (old != null) {
            evictCache(old);
        }
    }

    // ===================== 读操作：优先 Redis =====================

    @Override
    public RecordModelSettingEntity getByFileAndQuestionType(String fileId, String questionType) {
        if (!StringUtils.hasText(fileId) || !StringUtils.hasText(questionType)) return null;

        // 1) 先查缓存
        RecordModelSettingEntity cached = getFromFileCache(fileId, questionType);
        if (cached != null) return cached;

        // 2) 回源 DB
        RecordModelSettingEntity db = getOne(
                new LambdaQueryWrapper<RecordModelSettingEntity>()
                        .eq(RecordModelSettingEntity::getFileId, fileId)
                        .eq(RecordModelSettingEntity::getQuestionType, questionType)
                        .last("limit 1")
        );
        if (db != null) {
            putCache(db);
        }
        return db;
    }

    @Override
    public RecordModelSettingEntity getByConfigAndQuestionType(String configPackageId, String questionType) {
        if (!StringUtils.hasText(configPackageId) || !StringUtils.hasText(questionType)) return null;

        // 1) 先查缓存
        RecordModelSettingEntity cached = getFromConfigCache(configPackageId, questionType);
        if (cached != null) return cached;

        // 2) 回源 DB
        RecordModelSettingEntity db = getOne(
                new LambdaQueryWrapper<RecordModelSettingEntity>()
                        .eq(RecordModelSettingEntity::getConfigPackageId, configPackageId)
                        .eq(RecordModelSettingEntity::getQuestionType, questionType)
                        .last("limit 1")
        );
        if (db != null) {
            putCache(db);
        }
        return db;
    }

    // ===================== 手动刷新 =====================

    @Override
    public void refreshCacheForFile(String fileId) {
        if (!StringUtils.hasText(fileId)) return;
        List<RecordModelSettingEntity> list = list(
                new LambdaQueryWrapper<RecordModelSettingEntity>()
                        .eq(RecordModelSettingEntity::getFileId, fileId)
        );
        // 先清空后重建
        try {
            stringRedisTemplate.delete(KEY_FILE_PREFIX + fileId);
        } catch (DataAccessException e) {
            log.warn("Redis 删除 file 哈希失败: {}", e.getMessage());
        }
        if (list != null) {
            for (RecordModelSettingEntity e : list) putCache(e);
        }
    }

    @Override
    public void refreshCacheForConfig(String configPackageId) {
        if (!StringUtils.hasText(configPackageId)) return;
        List<RecordModelSettingEntity> list = list(
                new LambdaQueryWrapper<RecordModelSettingEntity>()
                        .eq(RecordModelSettingEntity::getConfigPackageId, configPackageId)
        );
        try {
            stringRedisTemplate.delete(KEY_CFG_PREFIX + configPackageId);
        } catch (DataAccessException e) {
            log.warn("Redis 删除 config 哈希失败: {}", e.getMessage());
        }
        if (list != null) {
            for (RecordModelSettingEntity e : list) putCache(e);
        }
    }

    @Override
    public void refreshCacheById(Long id) {
        if (id == null) return;
        RecordModelSettingEntity db = getById(id);
        if (db == null) {
            // 记录不存在，尽力清理两个维度（没有 questionType 等信息，无法精准清理）
            return;
        }
        putCache(db);
    }

    // ===================== Redis Helpers =====================

    private RecordModelSettingEntity getFromFileCache(String fileId, String questionType) {
        try {
            Object v = stringRedisTemplate.opsForHash()
                    .get(KEY_FILE_PREFIX + fileId, questionType);
            if (v == null) return null;
            return objectMapper.readValue(String.valueOf(v), RecordModelSettingEntity.class);
        } catch (Exception e) {
            log.warn("读取缓存失败 FILE({})#{}: {}", fileId, questionType, e.getMessage());
            return null;
        }
    }

    private RecordModelSettingEntity getFromConfigCache(String configPackageId, String questionType) {
        try {
            Object v = stringRedisTemplate.opsForHash()
                    .get(KEY_CFG_PREFIX + configPackageId, questionType);
            if (v == null) return null;
            return objectMapper.readValue(String.valueOf(v), RecordModelSettingEntity.class);
        } catch (Exception e) {
            log.warn("读取缓存失败 CFG({})#{}: {}", configPackageId, questionType, e.getMessage());
            return null;
        }
    }

    private void putCache(RecordModelSettingEntity e) {
        if (e == null || StrUtil.isBlank(e.getQuestionType())) return;
        try {
            String json = objectMapper.writeValueAsString(e);
            // file 维度
            if (StrUtil.isNotBlank(e.getFileId())) {
                stringRedisTemplate.opsForHash()
                        .put(KEY_FILE_PREFIX + e.getFileId(), e.getQuestionType(), json);
            }
            // config 维度
            if (StrUtil.isNotBlank(e.getConfigPackageId())) {
                stringRedisTemplate.opsForHash()
                        .put(KEY_CFG_PREFIX + e.getConfigPackageId(), e.getQuestionType(), json);
            }
        } catch (Exception ex) {
            log.warn("写入缓存失败: {}", ex.getMessage());
        }
    }

    private void evictCache(RecordModelSettingEntity e) {
        if (e == null || StrUtil.isBlank(e.getQuestionType())) return;
        try {
            if (StrUtil.isNotBlank(e.getFileId())) {
                stringRedisTemplate.opsForHash()
                        .delete(KEY_FILE_PREFIX + e.getFileId(), e.getQuestionType());
            }
            if (StrUtil.isNotBlank(e.getConfigPackageId())) {
                stringRedisTemplate.opsForHash()
                        .delete(KEY_CFG_PREFIX + e.getConfigPackageId(), e.getQuestionType());
            }
        } catch (DataAccessException ex) {
            log.warn("删除缓存失败: {}", ex.getMessage());
        }
    }
}
