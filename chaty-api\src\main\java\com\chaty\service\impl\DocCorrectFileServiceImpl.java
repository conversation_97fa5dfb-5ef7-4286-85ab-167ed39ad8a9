package com.chaty.service.impl;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chaty.dto.*;
import com.chaty.entity.*;
import com.chaty.entity.admin.ClassStatisticDataReq;
import com.chaty.mapper.*;
import com.chaty.service.*;
import com.chaty.service.cache.DefaultModelRedisService;
import com.chaty.service.cache.ModelSettingService;
import com.chaty.service.cache.QuestionTypeService;
import com.chaty.service.cache.RecordModelSettingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.*;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.enums.AIModelConsts;
import com.chaty.enums.CorrectEnums;
import com.chaty.enums.CorrectEnums.CorrectRecordStatus;
import com.chaty.enums.DocCorrectFileConsts;
import com.chaty.security.AuthUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;

@Slf4j
@Service
public class DocCorrectFileServiceImpl extends ServiceImpl<DocCorrectFileMapper, DocCorrectFile>
        implements DocCorrectFileService {
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectRecordService docCorrectRecordService;
    @Resource
    private DocCorrectTaskService docCorrectTaskService;
    @Resource
    private DocFileConfigService docFileConfigService;
    @Resource
    private ModelSettingService modelSettingService;
    @Resource
    private RecordModelSettingService recordModelSettingService;
    @Resource
    private DocCorrectConfigService docCorrectConfigService;
    @Autowired
    private DocCorrectConfigPackageMapper docCorrectConfigPackageMapper;
    @Resource
    private DocCorrectConfigPackageService docCorrectConfigPackageService;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private QuestionTypeService questionTypeService;
    @Value("${essay.apiUserId}")
    private String apiUserId;

    @Resource
    private FileService fileService;

    @Resource
    private UserMapper userMapper;
    @Autowired
    private DefaultModelRedisService defaultModelRedisService;

    @Override
    public void add(DocCorrectFileDTO param) {
        docCorrectFileMapper.insert(param);
    }

    @Override
    public void delete(String id) {
        docCorrectFileMapper.deleteById(id);
    }

    @Override
    public DocCorrectFileDTO getById(String id) {
        DocCorrectFile file = docCorrectFileMapper.selectById(id);
        return BeanUtil.copyProperties(file, DocCorrectFileDTO.class);
    }

    @Override
    public IPage<DocCorrectFileDTO> page(DocCorrectFileDTO param) {
        // 1. 构造分页查询
        Wrapper<DocCorrectFile> wrapper = Wrappers.lambdaQuery(DocCorrectFile.class)
                .like(StrUtil.isNotBlank(param.getName()), DocCorrectFile::getName, param.getName())
                .eq(Objects.nonNull(param.getStatus()), DocCorrectFile::getStatus, param.getStatus())
                .eq(Objects.nonNull(param.getIsEssay()), DocCorrectFile::getIsEssay, param.getIsEssay())
                .eq(DocCorrectFile::getDeleted, 0)
//                .eq(DocCorrectFile::getCreator, AuthUtil.getLoginUser().getId())
                .orderByAsc(DocCorrectFile::getStatus)
                .orderByDesc(DocCorrectFile::getCreateTime, DocCorrectFile::getId);

        IPage<DocCorrectFile> pageRes = docCorrectFileMapper.selectPage(param.getPage().mpPage(), wrapper);

        // 2. 如果不需要 configIds 就直接返回
        if (!Boolean.TRUE.equals(param.getNeedConfigIds())) {
            return pageRes.convert(file -> BeanUtil.copyProperties(file, DocCorrectFileDTO.class));
        }

        // 3. 收集本页所有 fileId
        List<String> fileIds = pageRes.getRecords().stream()
                .map(DocCorrectFile::getId)
                .collect(Collectors.toList());

        if (fileIds.isEmpty()) {
            return pageRes.convert(file -> BeanUtil.copyProperties(file, DocCorrectFileDTO.class));
        }

        // 4. 批量查询这些 fileId 对应的所有任务，只取 fileId 和 configId
        List<DocCorrectTask> tasks = docCorrectTaskMapper.selectList(
                Wrappers.<DocCorrectTask>lambdaQuery()
                        .in(DocCorrectTask::getFileId, fileIds)
                        .select(DocCorrectTask::getFileId, DocCorrectTask::getConfigId)
        );

        // 5. 按 fileId 分组，映射到 List<configId>
        Map<String, List<String>> configIdsByFile = tasks.stream()
                .collect(Collectors.groupingBy(
                        DocCorrectTask::getFileId,
                        Collectors.mapping(DocCorrectTask::getConfigId, Collectors.toList())
                ));

        // 6. 转换 DTO，同时把分组结果序列化为 JSON 字符串
        return pageRes.convert(file -> {
            DocCorrectFileDTO dto = BeanUtil.copyProperties(file, DocCorrectFileDTO.class);
            List<String> cids = configIdsByFile.getOrDefault(file.getId(), Collections.emptyList());
            String json = cn.hutool.json.JSONUtil.toJsonStr(cids);
            dto.setConfigIds(json);
            return dto;
        });
    }


    @Override
    public void update(DocCorrectFileDTO param) {
        docCorrectFileMapper.updateById(param);
    }

    @Transactional
    @Override
    public DocCorrectFileDTO createFile(DocCorrectFileDTO param, Boolean isForApiUse) {

        User user = AuthUtil.getLoginUser();
        // 新增
        param.setStatus(DocCorrectFileConsts.Status.WAIT); // 设置状态
        if (isForApiUse) {
            param.setCreator(apiUserId);
        } else {
            param.setCreator(user.getId());
        }
        String generatedId = UUID.randomUUID().toString();
        param.setId(generatedId); // 设置生成的 id
        docCorrectFileMapper.insert(param);

        List<DocCorrectRecord> records = new ArrayList<>();
        List<JSONObject> pages = param.getPages();
        for (int i = 0; i < pages.size(); i++) {
            JSONObject page = pages.get(i);
            // 新增批改任务
            DocCorrectTask task = new DocCorrectTask();
            task.setName(String.format("%s(页%s)", param.getName(), i + 1));
            task.setConfigId(page.getStr("configId"));
            task.setStatus(CorrectEnums.CorrectTakStatus.WAIT);
            task.setFileId(param.getId());
            if (isForApiUse) {
                task.setCreator(apiUserId);
            } else {
                task.setCreator(user.getId());
            }
            Integer offsetX = 0;
            Integer offsetY = 0;
            if (Objects.nonNull(param.getOffset()) && param.getOffset().size() > i) {
                OffsetXYDTO offsetXYDTO = param.getOffset().get(i);
                if (Objects.nonNull(offsetXYDTO)) {
                    // 取出偏移量
                    if (Objects.nonNull(offsetXYDTO.getOffsetX())) {
                        offsetX = offsetXYDTO.getOffsetX();
                    }
                    if (Objects.nonNull(offsetXYDTO.getOffsetY())) {
                        offsetY = offsetXYDTO.getOffsetY();
                    }
                }
            }
            task.setOffsetX(offsetX);
            task.setOffsetY(offsetY);
            task.setIsEssay(param.getIsEssay());

            JSONObject correctConfig  = new JSONObject();
            correctConfig.set("aimodel", param.getModelValue());
            correctConfig.set("ocrType", "2");
            correctConfig.set("responseFormat", true);
            correctConfig.set("jsonobject", param.getJsonobject());
            correctConfig.set("jsonschema", param.getJsonschema());
            task.setCorrectConfig(JSONUtil.toJsonStr(correctConfig));
            if (Objects.nonNull(param.getModelRequestId())) {
                task.setModelRequestId(param.getModelRequestId());
            }
            docCorrectTaskMapper.insert(task);
            // 新增批改记录
            List<JSONObject> docs = page.getJSONArray("docs").toList(JSONObject.class);
            for (int j = 0; j < docs.size(); j++) {
                JSONObject doc = docs.get(j);
                DocCorrectRecord record = new DocCorrectRecord();
                record.setDocurl(doc.getStr("url"));
                record.setDocname(String.format("%s_%03d", task.getName(), j + 1));
                record.setStatus(CorrectRecordStatus.UNCORRECT);
                record.setTaskId(task.getId());
                record.setOffsetX(offsetX);
                record.setOffsetY(offsetY);
                record.setFileId(param.getId());
                records.add(record);
            }
        }
        // 批量新增试卷
        docCorrectRecordService.saveBatch(records);

        // 保存试卷配置
        docFileConfigService.saveByFile(param);

        // 保存模型设置
        String aiModel = null;
        if (Objects.nonNull(param.getQuestionTypeModelIdMap()) && !param.getQuestionTypeModelIdMap().isEmpty()) {
            for (Map.Entry<String, Integer> entry : param.getQuestionTypeModelIdMap().entrySet()) {
                String questionType = entry.getKey();
                Integer modelRequestId = entry.getValue();
                if (modelRequestId == null || modelRequestId <= 0) {
                    continue;
                }
                RecordModelSettingEntity setting = new RecordModelSettingEntity();
                setting.setFileId(param.getId());
                setting.setType("file");
                setting.setQuestionType(questionType);
                setting.setModelSettingId(modelRequestId);
                recordModelSettingService.save(setting);
            }

            IPage<RecordModelSettingEntity> recordModelSettingEntityIPage = recordModelSettingService.page(1, 10, "config", null, param.getConfigPackageId(), null);
            if (recordModelSettingEntityIPage.getRecords().isEmpty()) {
                // 第一次-保存配置包的模型设置
                for (Map.Entry<String, Integer> entry : param.getQuestionTypeModelIdMap().entrySet()) {
                    String questionType = entry.getKey();
                    Integer modelRequestId = entry.getValue();
                    if (modelRequestId == null || modelRequestId <= 0) {
                        continue;
                    }
                    RecordModelSettingEntity setting = new RecordModelSettingEntity();
                    setting.setConfigPackageId(param.getConfigPackageId());
                    setting.setType("config");
                    setting.setQuestionType(questionType);
                    setting.setModelSettingId(modelRequestId);
                    recordModelSettingService.save(setting);
                }
            }

            // 设置默认模型
            if (param.getQuestionTypeModelIdMap().containsKey("通用")) {
                Integer modelRequestId = param.getQuestionTypeModelIdMap().get("通用");
                ModelSetting modelSetting = modelSettingService.getByIdCache(modelRequestId);
                if (modelSetting != null) {
                    aiModel = modelSetting.getModelValue();
                }
            } else {
                // 选第一个模型的
                Integer modelRequestId = param.getQuestionTypeModelIdMap().values().iterator().next();
                ModelSetting modelSetting = modelSettingService.getByIdCache(modelRequestId);
                if (modelSetting != null) {
                    aiModel = modelSetting.getModelValue();
                }
            }
        } else {
            // 找到通用的默认
            QuestionType questionType = questionTypeService.getByName("通用");
            if (questionType != null ) {
                try {
                    Integer modelRequestId = questionType.getDefaultModelRequestId();
                    ModelSetting modelSetting = modelSettingService.getByIdCache(modelRequestId);
                    if (modelSetting != null) {
                        aiModel = modelSetting.getModelValue();
                    }
                } catch (NumberFormatException e) {
                    log.warn("通用题型的默认模型设置 ID 不是有效的整数: {}", questionType.getDefaultModelRequestId());
                }
            }
        }
        // 更新默认模型
        DocCorrectFile updateFile = new DocCorrectFile();
        updateFile.setId(param.getId());
        updateFile.setModelValue(aiModel);
        docCorrectFileMapper.updateById(updateFile);

        // 是否批改？
        if (Objects.equals("correct", param.getAction())) {
            doCorrect(param);
        }
        return param;
    }

    @Transactional
    @Override
    public void doCorrect(DocCorrectFileDTO params) {
        List<DocCorrectTask> tasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                .eq(DocCorrectTask::getFileId, params.getId())
                .eq(DocCorrectTask::getDeleted, 0));

        for (DocCorrectTask task : tasks) {
            // 只批改配置好的
            if (StrUtil.isBlank(task.getConfigId())) {
                continue;
            }
            DocCorrectTaskDTO taskDTO = BeanUtil.copyProperties(task, DocCorrectTaskDTO.class);
            // 修改默认
            if (StrUtil.isBlank(params.getModelValue())) {
                taskDTO.setAimodel(AIModelConsts.GPT_4O_20240806);
            } else {
                taskDTO.setAimodel(params.getModelValue());
            }

            // 1. 先将 correctConfig 字符串解析为 JSONObject
            JSONObject correctConfigObj;
            if (StrUtil.isBlank(task.getCorrectConfig())) {
                correctConfigObj = new JSONObject();
                correctConfigObj.set("jsonobject", defaultModelRedisService.getJsonobject());
                correctConfigObj.set("jsonschema", defaultModelRedisService.getJsonschema());
            } else {
                correctConfigObj = JSONUtil.parseObj(task.getCorrectConfig());
            }

            // 2. 从 JSONObject 中获取 jsonobject 和 jsonschema 参数，带默认值
            Boolean jsonobject = correctConfigObj.getBool("jsonobject", defaultModelRedisService.getJsonobject());
            Boolean jsonschema = correctConfigObj.getBool("jsonschema", defaultModelRedisService.getJsonschema());
            taskDTO.setJsonobject(jsonobject);
            taskDTO.setJsonschema(jsonschema);

            taskDTO.setOcrType("2");
            taskDTO.setResponseFormat(true);
            docCorrectTaskService.execute(taskDTO);
        }

        // 更新状态
        DocCorrectFile file = new DocCorrectFile();
        file.setId(params.getId());
        file.setStatus(DocCorrectFileConsts.Status.PROCESSING);
        docCorrectFileMapper.updateById(file);
    }


    @Override
    public JSONArray getClassStatisticData(ClassStatisticDataReq req) {
        JSONArray res = new JSONArray();
        JSONArray classStatisticData = new JSONArray();
        JSONArray studentRows = new JSONArray();
        LinkedHashSet<String> globalTypeOrder = new LinkedHashSet<>();

        for (ClassStatisticDataItem classStatisticDataItem : req.getFileDetails()) {
            List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                    .eq(DocCorrectTask::getDeleted, 0)
                    .eq(DocCorrectTask::getFileId, classStatisticDataItem.getFileId()));

            List<String> names = new ArrayList<>();
            List<String> studentNumbers = new ArrayList<>();
            List<String> docNames = new ArrayList<>();
            JSONArray studentScores = new JSONArray();

            // 第一次遍历：收集分数类型顺序
            List<List<DocCorrectRecordDTO>> recordsList = new ArrayList<>();
            List<DocCorrectConfig> docCorrectConfigs = new ArrayList<>();
            for (DocCorrectTask docCorrectTask : docCorrectTasks) {
                List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(docCorrectTask.getId(), null);
                DocCorrectConfig config = docCorrectConfigService.getById(docCorrectTask.getConfigId());
                docCorrectConfigs.add(config);
                List<DocCorrectRecordDTO> recordDTOS = new ArrayList<>();
                for (DocCorrectRecord record : records) {
                    DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
                    dto.setConfig(BeanUtil.copyProperties(config, DocCorrectConfigDTO.class));
                    recordDTOS.add(dto);
                    JSONObject scoreTypeMap = dto.getScore().getJSONObject("scoreTypeMap");
                    if (scoreTypeMap != null) {
                        globalTypeOrder.addAll(scoreTypeMap.keySet());
                    }
                }
                recordsList.add(recordDTOS);
            }

            List<String> typeOrder = new ArrayList<>(globalTypeOrder);
            Map<String, BigDecimal> aggregateScored = new HashMap<>();
            Integer hege = 0;
            Integer youxiu = 0;
            BigDecimal sumTotalScore = BigDecimal.ZERO;
            Integer studentCount = recordsList.get(0).size();

            // 新增：等级统计数据结构
            Map<String, BigDecimal> typeSum = new HashMap<>();
            BigDecimal totalSum = BigDecimal.ZERO;
            JSONObject ranges = req.getRanges();
            // 第二次遍历：处理实际数据
            for (List<DocCorrectRecordDTO> recordDTOS : recordsList) {
                int idx = 0;
                for (DocCorrectRecordDTO docCorrectRecordDTO : recordDTOS) {
                    JSONObject score = docCorrectRecordDTO.getScore();

                    if (studentScores.size() < studentCount) {
                        studentScores.put(score);
                        names.add(docCorrectRecordDTO.getIdentify());
                        studentNumbers.add(docCorrectRecordDTO.getStudentNumber());
                        docNames.add(docCorrectRecordDTO.getDocname());
                    } else {
                        studentScores.put(idx, docCorrectRecordDTO.mergeScore(score, studentScores.getJSONObject(idx)));
                    }

                    JSONObject scoreTypeMap = score.getJSONObject("scoreTypeMap");
                    BigDecimal totalScore = score.getBigDecimal("scored");

                    for (String type : typeOrder) {
                        BigDecimal typeScore = (scoreTypeMap != null && scoreTypeMap.containsKey(type))
                                ? scoreTypeMap.getJSONObject(type).getBigDecimal("scored", BigDecimal.ZERO)
                                : BigDecimal.ZERO;
                        typeSum.merge(type, typeScore, BigDecimal::add);
                    }
                    totalSum = totalSum.add(totalScore);
                    idx++;
                }
            }


            // 计算统计数据
            for (int i = 0; i < studentScores.size(); i++) {
                JSONObject score = studentScores.getJSONObject(i);
                BigDecimal totalScore = score.getBigDecimal("scored");
                sumTotalScore = sumTotalScore.add(totalScore);

                if (totalScore.compareTo(BigDecimal.valueOf(req.getHegeScore())) >= 0) hege++;
                if (totalScore.compareTo(BigDecimal.valueOf(req.getYouxiuScore())) >= 0) youxiu++;

                JSONObject scoreTypeMap = score.getJSONObject("scoreTypeMap");
                if (scoreTypeMap != null) {
                    for (String type : typeOrder) {
                        JSONObject typeObj = scoreTypeMap.getJSONObject(type);
                        if (typeObj != null) {
                            BigDecimal typeScored = typeObj.getBigDecimal("scored", BigDecimal.ZERO);
                            aggregateScored.put(type, aggregateScored.getOrDefault(type, BigDecimal.ZERO).add(typeScored));
                        }
                    }
                }
            }

            // 构建班级统计行
            JSONArray classDataRow = new JSONArray();
            classDataRow.put(classStatisticDataItem.getClassName());
            classDataRow.put(classStatisticDataItem.getName());
            for (String type : typeOrder) {
                BigDecimal avg = aggregateScored.getOrDefault(type, BigDecimal.ZERO)
                        .divide(BigDecimal.valueOf(studentCount), 2, RoundingMode.HALF_UP);
                classDataRow.put(avg);
            }
            classDataRow.put(sumTotalScore.divide(BigDecimal.valueOf(studentCount), 2, RoundingMode.HALF_UP));
            classDataRow.put(String.format("%d/%d", hege, studentCount));
            classDataRow.put(String.format("%d/%d", youxiu, studentCount));

            if (classStatisticData.isEmpty()) {
                JSONArray header = new JSONArray().put("班级").put("试卷名称");
                header.addAll(typeOrder);
                header.add("总分");
                header.add("合格率");
                header.add("优秀率");
                classStatisticData.put(header);
            }
            classStatisticData.put(classDataRow);

            // 构建学生详细表格
            JSONArray studentTable = new JSONArray();
            JSONArray studentHeader = new JSONArray()
                    .put("序号").put("学号").put("姓名");
            for (String type : typeOrder) studentHeader.add(type + "分数");
            studentHeader.add("总分");
            for (String type : typeOrder) studentHeader.add(type + "等级");
            studentHeader.add("总分等级");
            studentTable.put(studentHeader);

            Map<String, Map<String, Integer>> levelCounts = new HashMap<>();
            // 填充学生数据行
            for (int i = 0; i < studentScores.size(); i++) {
                JSONArray studentRow = new JSONArray();
                String[] docParts = docNames.get(i).split("_");
                studentRow.put(docParts[docParts.length - 1])
                        .put(studentNumbers.get(i))
                        .put(names.get(i));

                JSONObject scoreData = studentScores.getJSONObject(i);
                JSONObject scoreTypeMap = scoreData.getJSONObject("scoreTypeMap");

                for (String type : typeOrder) {
                    BigDecimal score = (scoreTypeMap != null && scoreTypeMap.containsKey(type))
                            ? scoreTypeMap.getJSONObject(type).getBigDecimal("scored", BigDecimal.ZERO)
                            : BigDecimal.ZERO;
                    studentRow.put(score.setScale(1, RoundingMode.HALF_UP));
                }

                BigDecimal totalScore = scoreData.getBigDecimal("scored").setScale(1, RoundingMode.HALF_UP);
                studentRow.put(totalScore);

                for (String type : typeOrder) {
                    BigDecimal typeScore = (scoreTypeMap != null && scoreTypeMap.containsKey(type))
                            ? scoreTypeMap.getJSONObject(type).getBigDecimal("scored", BigDecimal.ZERO)
                            : BigDecimal.ZERO;
                    String level = getLevel(typeScore, ranges.getJSONArray(type));
                    studentRow.put(level);
                    levelCounts.computeIfAbsent(type, k -> new HashMap<>()).merge(level, 1, Integer::sum);
                }

                JSONArray totalRanges = req.getRanges().getJSONArray("总分");
                String level = getLevel(totalScore, totalRanges);
                studentRow.put(level);
                levelCounts.computeIfAbsent("总分", k -> new HashMap<>()).merge(level, 1, Integer::sum);
                studentTable.put(studentRow);
            }
            // 按照学号排序
            if (Boolean.TRUE.equals(req.getOrderByStudentNumber())) {
                // studentTable是JSONArray 排除第一行表头进行排序
                sortStudentTable(studentTable);
            }

            // 等级数量统计（保持不变）
            Set<String> allLevels = new LinkedHashSet<>();
            for (String type : typeOrder) {
                JSONArray rangesItem = ranges.getJSONArray(type);
                if (rangesItem == null) continue;
                for (int i = 0; i < rangesItem.size(); i++) {
                    allLevels.add(rangesItem.getJSONObject(i).getStr("name"));
                }
            }
            JSONArray totalRanges = req.getRanges().getJSONArray("总分");
            for (int i = 0; i < totalRanges.size(); i++) {
                allLevels.add(totalRanges.getJSONObject(i).getStr("name"));
            }

            // 修改点：从classDataRow获取平均分数据
            if (!studentScores.isEmpty()) {
                JSONArray avgRow = new JSONArray().put("平均分").put("").put("");

                // 从班级统计行获取数据（试卷名称到合格率之间的列）
                int startIdx = 2; // 跳过班级和试卷名称
                int endIdx = classDataRow.size() - 3; // 排除合格率和优秀率

                for (int i = startIdx; i <= endIdx; i++) {
                    BigDecimal val = classDataRow.getBigDecimal(i).setScale(1, RoundingMode.HALF_UP);
                    avgRow.put(val);
                }

                // 填充等级
                String level = allLevels.iterator().hasNext() ? allLevels.iterator().next() : null;
                for (String type : typeOrder) {
                    avgRow.put(levelCounts.getOrDefault(type, Collections.emptyMap()).getOrDefault(level, 0).toString() + level);
                }
                avgRow.put(levelCounts.getOrDefault("总分", Collections.emptyMap()).getOrDefault(level, 0).toString() + level);

                studentTable.put(avgRow);
            }

            int idx = 0;
            for (String level : allLevels) {
                // 第一个元素填充在了平均分行
                idx++;
                if (idx == 1) {
                    continue;
                }
                JSONArray levelRow = new JSONArray();
                for (int i = 0; i < typeOrder.size() + 4; i++) levelRow.put("");
                for (String type : typeOrder) {
                    levelRow.put(levelCounts.getOrDefault(type, Collections.emptyMap()).getOrDefault(level, 0).toString() + level);
                }
                levelRow.put(levelCounts.getOrDefault("总分", Collections.emptyMap()).getOrDefault(level, 0).toString() + level);
                studentTable.put(levelRow);
            }

            studentRows.put(studentTable);
        }

        res.add(classStatisticData);
        res.addAll(studentRows);
        return res;
    }

    public void sortStudentTable(JSONArray studentTable) {
        if (studentTable == null || studentTable.size() <= 1) {
            // 无需排序
            return;
        }

        // 取出表头
        JSONArray header = studentTable.getJSONArray(0);
        JSONArray headerCopy = new JSONArray();
        headerCopy.addAll(header);

        // 取出数据部分
        List<Object> dataRows = new ArrayList<>(studentTable.subList(1, studentTable.size()));

        dataRows.sort(new Comparator<Object>() {
            @Override
            public int compare(Object o1, Object o2) {
                // 这里假设每一行是JSONArray
                JSONArray row1 = (JSONArray) o1;
                JSONArray row2 = (JSONArray) o2;

                String studentNum1 = row1.get(1) == null ? "" : row1.get(1).toString();
                String studentNum2 = row2.get(1) == null ? "" : row2.get(1).toString();

                String studentName1 = row1.get(2) == null ? "" : row1.get(2).toString();
                String studentName2 = row2.get(2) == null ? "" : row2.get(2).toString();


                // 判断学号是否无效
                boolean invalid1 = isInvalidStudentNum(studentNum1);
                boolean invalid2 = isInvalidStudentNum(studentNum2);

                // 无效排后面
                if (invalid1 && !invalid2) return -1;
                if (!invalid1 && invalid2) return 1;

                if (!invalid1 && !invalid2) {
                    // 学号均有效，按降序（大->小）排序
                    // 转成整数比较，若非数字则改成其他比较逻辑
                    try {
                        long num1 = Long.parseLong(studentNum1);
                        long num2 = Long.parseLong(studentNum2);
                        if (num1 != num2) {
                            return Long.compare(num1, num2); // 降序
                        }
                    } catch (NumberFormatException e) {
                        // 如果学号不是纯数字，可改为字符串比较或忽略异常
                        int cmp = studentNum2.compareTo(studentNum1);
                        if (cmp != 0) {
                            return cmp;
                        }
                    }
                }

                // 学号相等或无效时，按姓名升序排列
                if (studentName1 == null && studentName2 != null) return -1;
                if (studentName1 != null && studentName2 == null) return 1;
                if (studentName1 == null && studentName2 == null) return 0;
                return studentName1.compareTo(studentName2);
            }
        });

        // 重新组合JSONArray：先放表头，再放排序后的数据
        studentTable.clear();
        studentTable.add(headerCopy);
        studentTable.addAll(dataRows);
    }

    private boolean isInvalidStudentNum(String s) {
        return s == null || s.isEmpty() || "无".equals(s);
    }

    private String getLevel(BigDecimal score, JSONArray ranges) {
        if (ranges == null || ranges.isEmpty()) return "未知";
        for (int i = 0; i < ranges.size(); i++) {
            JSONObject range = ranges.getJSONObject(i);
            BigDecimal min = range.getBigDecimal("minn");
            BigDecimal max = range.getBigDecimal("maxx");
            if (min != null && max != null && score.compareTo(min) >= 0 && score.compareTo(max) <= 0) {
                return range.getStr("name");
            }
        }
        return "未知";
    }

    @Override
    public List<DocCorrectFile> getDocCorrectFileListByConfigPackageId(String configPackageId) {
        DocCorrectConfigPackage docCorrectConfigPackage = docCorrectConfigPackageService.getById(configPackageId);
        JSONArray configIds = JSONUtil.parseArray(docCorrectConfigPackage.getConfig());
        List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                .in(DocCorrectTask::getConfigId, configIds.getStr(0))
                .eq(DocCorrectTask::getDeleted, 0));
        if (CollUtil.isEmpty(docCorrectTasks)) {
            return Collections.emptyList();
        }
        List<DocCorrectFile> docCorrectFiles = docCorrectFileMapper.selectList(Wrappers.lambdaQuery(DocCorrectFile.class)
                .in(DocCorrectFile::getId, docCorrectTasks.stream().map(DocCorrectTask::getFileId).toArray()));
        return docCorrectFiles;
    }

    @Override
    public String generateClassStatisticExcel(JSONArray classStatisticData) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("班级统计");
            JSONArray classStats = classStatisticData.getJSONArray(0);
            JSONArray header = classStats.getJSONArray(0);
            int numCols = header.size();

            // 写表头
            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            for (int i = 0; i < numCols; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(header.getStr(i));
                cell.setCellStyle(headerStyle);
            }

            // 写数据行并计算百分比
            int rowNum = 1;
            for (int i = 1; i < classStats.size(); i++) {
                JSONArray rowData = classStats.getJSONArray(i);
                Row row = sheet.createRow(rowNum++);
                for (int j = 0; j < numCols; j++) {
                    Object v = rowData.get(j);
                    Cell c = row.createCell(j);
                    if (v instanceof Number) c.setCellValue(((Number) v).doubleValue());
                    else c.setCellValue(v.toString());
                }
                // 合格率
                String[] p = rowData.getStr(numCols - 2).split("/");
                double pass = BigDecimal.valueOf(
                        Double.parseDouble(p[0]) / Double.parseDouble(p[1])
                ).setScale(4, RoundingMode.HALF_UP).doubleValue();
                Cell passCell = row.getCell(numCols - 2);
                passCell.setCellValue(pass);
                // 优秀率
                String[] e = rowData.getStr(numCols - 1).split("/");
                double excel = BigDecimal.valueOf(
                        Double.parseDouble(e[0]) / Double.parseDouble(e[1])
                ).setScale(4, RoundingMode.HALF_UP).doubleValue();
                Cell excelCell = row.getCell(numCols - 1);
                excelCell.setCellValue(excel);

                CellStyle pct = workbook.createCellStyle();
                pct.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));
                passCell.setCellStyle(pct);
                excelCell.setCellStyle(pct);
            }

            int lastRow = sheet.getLastRowNum();
            Drawing<?> drawing = sheet.createDrawingPatriarch();

            // 柱状图（总分）
            ClientAnchor barAnchor = drawing.createAnchor(0, 0, 0, 0, 0, lastRow + 2, numCols, lastRow + 20);
            XSSFChart barChart = ((XSSFDrawing) drawing).createChart(barAnchor);
            barChart.setTitleText("班级平均分统计");
            XDDFCategoryAxis bottom = barChart.createCategoryAxis(AxisPosition.BOTTOM);
            XDDFValueAxis left = barChart.createValueAxis(AxisPosition.LEFT);
            left.setCrosses(AxisCrosses.AUTO_ZERO);
            left.setMinimum(0.0);
            left.setMaximum(100.0);

            XDDFDataSource<String> classes = XDDFDataSourcesFactory.fromStringCellRange(
                    sheet, new CellRangeAddress(1, lastRow, 0, 0)
            );
            int totalCol = numCols - 3;
            XDDFNumericalDataSource<Double> totals = XDDFDataSourcesFactory.fromNumericCellRange(
                    sheet, new CellRangeAddress(1, lastRow, totalCol, totalCol)
            );

            XDDFBarChartData barData = (XDDFBarChartData) barChart.createData(ChartTypes.BAR, bottom, left);
            barData.setBarDirection(BarDirection.COL);
            barData.setBarGrouping(BarGrouping.CLUSTERED);

            XDDFBarChartData.Series totalSeries = (XDDFBarChartData.Series) barData.addSeries(classes, totals);
            totalSeries.setTitle("总分", null);
            XDDFSolidFillProperties fill = new XDDFSolidFillProperties(
                    XDDFColor.from(new byte[]{(byte)0x2a, (byte)0x18, (byte)0x50})
            );
            totalSeries.setFillProperties(fill);

            barChart.plot(barData);
            // 调整位置，y轴和第一个柱子之间的距离
            CTValAx valAx = barChart
                    .getCTChart()
                    .getPlotArea()
                    .getValAxArray(0);
            if (valAx.isSetCrossBetween()) {
                valAx.getCrossBetween().setVal(STCrossBetween.BETWEEN);
            } else {
                valAx.addNewCrossBetween().setVal(STCrossBetween.BETWEEN);
            }

            CTPlotArea barPlot = barChart.getCTChart().getPlotArea();
            if (barPlot.sizeOfBarChartArray() > 0) {
                CTBarSer ser = barPlot.getBarChartArray(0).getSerArray(0);
                ser.addNewDLbls().addNewShowVal().setVal(true);
                ser.getDLbls().addNewShowLegendKey().setVal(false);
                ser.getDLbls().addNewShowCatName().setVal(false);
                ser.getDLbls().addNewShowSerName().setVal(false);
            }

            // 折线图（合格率与优秀率）
            ClientAnchor lineAnchor = drawing.createAnchor(0, 0, 0, 0, 0, lastRow + 21, numCols, lastRow + 40);
            XSSFChart lineChart = ((XSSFDrawing) drawing).createChart(lineAnchor);
            lineChart.setTitleText("合格率与优秀率");
            lineChart.getOrAddLegend().setPosition(org.apache.poi.xddf.usermodel.chart.LegendPosition.BOTTOM);

            XDDFCategoryAxis lBottom = lineChart.createCategoryAxis(AxisPosition.BOTTOM);
            XDDFValueAxis lLeft = lineChart.createValueAxis(AxisPosition.LEFT);
            lLeft.setCrosses(org.apache.poi.xddf.usermodel.chart.AxisCrosses.AUTO_ZERO);

            CTValAx lineValAx = lineChart.getCTChart().getPlotArea().getValAxArray(0);
            if (lineValAx.isSetCrossBetween()) {
                lineValAx.getCrossBetween().setVal(STCrossBetween.BETWEEN);
            } else {
                lineValAx.addNewCrossBetween().setVal(STCrossBetween.BETWEEN);
            }

            XDDFLineChartData lineData = (XDDFLineChartData) lineChart.createData(ChartTypes.LINE, lBottom, lLeft);
            XDDFDataSource<String> xNames = XDDFDataSourcesFactory.fromStringCellRange(
                    sheet, new CellRangeAddress(1, lastRow, 0, 0)
            );

            XDDFNumericalDataSource<Double> passSrc = XDDFDataSourcesFactory.fromNumericCellRange(
                    sheet, new CellRangeAddress(1, lastRow, numCols - 2, numCols - 2)
            );
            XDDFLineChartData.Series passSeries = (XDDFLineChartData.Series) lineData.addSeries(xNames, passSrc);
            passSeries.setTitle("合格率", null);

            XDDFNumericalDataSource<Double> excelSrc = XDDFDataSourcesFactory.fromNumericCellRange(
                    sheet, new CellRangeAddress(1, lastRow, numCols - 1, numCols - 1)
            );
            XDDFLineChartData.Series excelSeries = (XDDFLineChartData.Series) lineData.addSeries(xNames, excelSrc);
            excelSeries.setTitle("优秀率", null);

            lineChart.plot(lineData);

            CTPlotArea linePlot = lineChart.getCTChart().getPlotArea();
            if (linePlot.sizeOfLineChartArray() > 0) {
                for (CTLineSer ls : linePlot.getLineChartArray(0).getSerList()) {
                    ls.addNewDLbls().addNewShowVal().setVal(true);
                    ls.getDLbls().addNewShowLegendKey().setVal(false);
                    ls.getDLbls().addNewShowCatName().setVal(false);
                    ls.getDLbls().addNewShowSerName().setVal(false);
                }
            }

            for (int i = 0; i < numCols; i++) sheet.autoSizeColumn(i);
            sheet.setColumnWidth(0, 25 * 256);
            sheet.setColumnWidth(1, 25 * 256);

            processStudentSheets(workbook, classStatisticData, 1);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            String filename = IdUtil.fastSimpleUUID() + ".xlsx";
            return fileService.saveFile(out.toByteArray(), filename);
        } catch (Exception e) {
            throw new RuntimeException("生成Excel失败", e);
        }
    }

    @Override
    public String generateClassStatisticExcel4(JSONArray classStatisticData) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("班级统计");
            JSONArray classStats = classStatisticData.getJSONArray(0);
            JSONArray titleArr  = classStats.getJSONArray(0);
            JSONArray headerArr = classStats.getJSONArray(1);
            int numCols = headerArr.size();

            // 通用边框样式（细黑边框）
            BorderStyle thinBorder = BorderStyle.THIN;
            short black = IndexedColors.BLACK.getIndex();

            // 样式定义
            // 标题样式（左对齐）
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setFontName("微软雅黑");
            titleFont.setFontHeightInPoints((short)18);
            titleFont.setColor(IndexedColors.BLACK.getIndex());
            titleFont.setBold(true);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);  // 居中对齐
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 表头样式（左对齐）
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setColor(IndexedColors.BLACK.getIndex());
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.LEFT);  // 左对齐
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 添加边框
            headerStyle.setBorderTop(thinBorder);
            headerStyle.setBorderBottom(thinBorder);
            headerStyle.setBorderLeft(thinBorder);
            headerStyle.setBorderRight(thinBorder);
            headerStyle.setTopBorderColor(black);
            headerStyle.setBottomBorderColor(black);
            headerStyle.setLeftBorderColor(black);
            headerStyle.setRightBorderColor(black);

            // 数据样式（左对齐）
            DataFormat fmt = workbook.createDataFormat();
            // 百分比样式
            CellStyle percentStyle = workbook.createCellStyle();
            percentStyle.setDataFormat(fmt.getFormat("0.00%"));
            percentStyle.setAlignment(HorizontalAlignment.LEFT);  // 左对齐

            // 添加边框
            percentStyle.setBorderTop(thinBorder);
            percentStyle.setBorderBottom(thinBorder);
            percentStyle.setBorderLeft(thinBorder);
            percentStyle.setBorderRight(thinBorder);
            percentStyle.setTopBorderColor(black);
            percentStyle.setBottomBorderColor(black);
            percentStyle.setLeftBorderColor(black);
            percentStyle.setRightBorderColor(black);

            // 数值样式（保留两位小数）
            CellStyle decimalStyle = workbook.createCellStyle();
            decimalStyle.setDataFormat(fmt.getFormat("0.00"));
            decimalStyle.setAlignment(HorizontalAlignment.LEFT);  // 左对齐

            // 添加边框
            decimalStyle.setBorderTop(thinBorder);
            decimalStyle.setBorderBottom(thinBorder);
            decimalStyle.setBorderLeft(thinBorder);
            decimalStyle.setBorderRight(thinBorder);
            decimalStyle.setTopBorderColor(black);
            decimalStyle.setBottomBorderColor(black);
            decimalStyle.setLeftBorderColor(black);
            decimalStyle.setRightBorderColor(black);

            // 整数样式（用于第二列和第三列）
            CellStyle integerStyle = workbook.createCellStyle();
            integerStyle.setDataFormat(fmt.getFormat("0"));
            integerStyle.setAlignment(HorizontalAlignment.LEFT);  // 左对齐

            // 添加边框
            integerStyle.setBorderTop(thinBorder);
            integerStyle.setBorderBottom(thinBorder);
            integerStyle.setBorderLeft(thinBorder);
            integerStyle.setBorderRight(thinBorder);
            integerStyle.setTopBorderColor(black);
            integerStyle.setBottomBorderColor(black);
            integerStyle.setLeftBorderColor(black);
            integerStyle.setRightBorderColor(black);

            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setAlignment(HorizontalAlignment.LEFT);
            // 添加边框
            textStyle.setBorderTop(thinBorder);
            textStyle.setBorderBottom(thinBorder);
            textStyle.setBorderLeft(thinBorder);
            textStyle.setBorderRight(thinBorder);
            textStyle.setTopBorderColor(black);
            textStyle.setBottomBorderColor(black);
            textStyle.setLeftBorderColor(black);
            textStyle.setRightBorderColor(black);

            // 表格构建
            // 1. 标题行
            Row titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(titleArr.getStr(0));
            titleCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, numCols - 1));

            // 2. 表头行（全部列左对齐）
            Row headerRow = sheet.createRow(1);
            headerRow.setHeightInPoints(34);//两行高度
            for (int i = 0; i < numCols; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headerArr.getStr(i));
                cell.setCellStyle(headerStyle);  // 应用左对齐样式

                // 创建带自动换行的样式
                CellStyle wrapHeaderStyle = workbook.createCellStyle();
                wrapHeaderStyle.cloneStyleFrom(headerStyle);
                wrapHeaderStyle.setWrapText(true); // 启用自动换行
                cell.setCellStyle(wrapHeaderStyle);

                sheet.autoSizeColumn(i);  // 先自动调整
            }

            // 3. 数据行（全部列左对齐）
            for (int r = 2; r < classStats.size(); r++) {
                JSONArray dataArr = classStats.getJSONArray(r);
                Row row = sheet.createRow(r);
                for (int c = 0; c < numCols; c++) {
                    Cell cell = row.createCell(c);

                    String text = dataArr.getStr(c);
                    // 特殊处理班级列（第0列）
                    if (c == 0) {
                        cell.setCellType(CellType.STRING);
                        cell.setCellValue(text); // 直接写入字符串
                        cell.setCellStyle(textStyle);
                        continue; // 跳过后续处理
                    }

                    // 特殊处理第三列和第四列（只保留整数）
                    if (c == 2 || c == 3) {
                        if (text != null) {
                            text = text.trim();
                            try {
                                double d = Double.parseDouble(text);
                                cell.setCellValue((int)d); // 转换为整数
                                cell.setCellStyle(integerStyle);
                            } catch (NumberFormatException e) {
                                cell.setCellValue(text);
                                cell.setCellStyle(textStyle);
                            }
                        } else {
                            cell.setBlank();
                            cell.setCellStyle(textStyle);
                        }
                        continue; // 跳过后续处理
                    }

                    if (text != null) {
                        text = text.trim();
                        try {
                            if (text.endsWith("%")) {
                                // 百分比类型
                                String num = text.substring(0, text.length() - 1);
                                double d = Double.parseDouble(num) / 100.0;
                                cell.setCellValue(d);
                                cell.setCellStyle(percentStyle);  // 左对齐百分比
                            } else {
                                // 数值类型
                                double d = Double.parseDouble(text);
                                cell.setCellValue(d);
                                cell.setCellStyle(decimalStyle);  // 使用保留两位小数的样式
                            }
                        } catch (NumberFormatException e) {
                            // 文本类型（默认左对齐）
                            cell.setCellValue(text);
                            cell.setCellStyle(textStyle);
                        }
                    } else {
                        cell.setBlank();
                        cell.setCellStyle(textStyle);
                    }
                }
            }

            // 调整列宽
            // 先自动调整所有列
            for (int i = 0; i < numCols; i++) sheet.autoSizeColumn(i);

            // 设置最小宽度
            int mincolWidth = 10 * 256;
            for (int i = 0; i < numCols; i++) {
                if (sheet.getColumnWidth(i) < mincolWidth) {
                    sheet.setColumnWidth(i, mincolWidth);
                }
            }

            // 特定列宽度设置
            int narrowWidth = 8 * 256;
            sheet.setColumnWidth(0, narrowWidth);
            int widerWidth = 15 * 256;
            sheet.setColumnWidth(6, widerWidth);
            sheet.setColumnWidth(7, widerWidth);
            int widerWidth2 = 19 * 256;
            sheet.setColumnWidth(8, widerWidth2);
            // 第一个平均分图表
            int lastRow = sheet.getLastRowNum();
            Drawing<?> drawing = sheet.createDrawingPatriarch();
            ClientAnchor anchor = drawing.createAnchor(
                    0, 0, 0, 0,
                    0, lastRow + 2,
                    numCols, lastRow + 20
            );
            XSSFChart chart = ((XSSFDrawing) drawing).createChart((XSSFClientAnchor) anchor);
            chart.setTitleText("班级总分统计");

            XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
            XDDFValueAxis    leftAxis   = chart.createValueAxis(AxisPosition.LEFT);
            leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
            leftAxis.setMinimum(0.0);
            leftAxis.setMaximum(100.0);

            XDDFDataSource<String> categories = XDDFDataSourcesFactory
                    .fromStringCellRange(sheet,
                            new CellRangeAddress(2, lastRow, 0, 0));
            XDDFNumericalDataSource<Double> totals = XDDFDataSourcesFactory
                    .fromNumericCellRange(sheet,
                            new CellRangeAddress(2, lastRow, 4, 4));

            XDDFBarChartData barData = (XDDFBarChartData) chart
                    .createData(ChartTypes.BAR, bottomAxis, leftAxis);
            barData.setBarDirection(BarDirection.COL);
            barData.setBarGrouping(BarGrouping.CLUSTERED);

            XDDFBarChartData.Series series = (XDDFBarChartData.Series) barData
                    .addSeries(categories, totals);
            series.setTitle("总分", null);
            XDDFSolidFillProperties fill = new XDDFSolidFillProperties(
                    XDDFColor.from(new byte[]{ (byte)0x2a, (byte)0x18, (byte)0x50 })
            );
            series.setFillProperties(fill);
            chart.plot(barData);
            CTValAx valAx = chart.getCTChart()
                    .getPlotArea()
                    .getValAxArray(0);
            if (valAx.isSetCrossBetween()) {
                valAx.getCrossBetween().setVal(STCrossBetween.BETWEEN);
            } else {
                valAx.addNewCrossBetween().setVal(STCrossBetween.BETWEEN);
            }
            CTPlotArea plotArea = chart.getCTChart().getPlotArea();
            if (plotArea.sizeOfBarChartArray() > 0) {
                CTBarSer ctSer = plotArea.getBarChartArray(0).getSerArray(0);
                CTDLbls dLbls = ctSer.addNewDLbls();
                dLbls.addNewShowVal().setVal(true);
                dLbls.addNewShowLegendKey().setVal(false);
                dLbls.addNewShowCatName().setVal(false);
                dLbls.addNewShowSerName().setVal(false);
            }


            // 折线图
            ClientAnchor lineAnchor = drawing.createAnchor(0, 0, 0, 0, 0, lastRow + 21, numCols, lastRow + 40);
            XSSFChart lineChart = ((XSSFDrawing) drawing).createChart(lineAnchor);
            lineChart.setTitleText("合格率与班级前30学生占年级前30学生占比");
            lineChart.getOrAddLegend().setPosition(org.apache.poi.xddf.usermodel.chart.LegendPosition.BOTTOM);

            XDDFCategoryAxis lBottom = lineChart.createCategoryAxis(AxisPosition.BOTTOM);
            XDDFValueAxis lLeft = lineChart.createValueAxis(AxisPosition.LEFT);
            lLeft.setCrosses(org.apache.poi.xddf.usermodel.chart.AxisCrosses.AUTO_ZERO);

            // 设置CrossBetween属性（与柱状图保持一致）
            CTValAx lineValAx = lineChart.getCTChart().getPlotArea().getValAxArray(0);
            if (lineValAx.isSetCrossBetween()) {
                lineValAx.getCrossBetween().setVal(STCrossBetween.BETWEEN);
            } else {
                lineValAx.addNewCrossBetween().setVal(STCrossBetween.BETWEEN);
            }

            XDDFLineChartData lineData = (XDDFLineChartData) lineChart.createData(ChartTypes.LINE, lBottom, lLeft);
            XDDFDataSource<String> xNames = XDDFDataSourcesFactory.fromStringCellRange(
                    sheet, new CellRangeAddress(2, lastRow, 0, 0)
            );

            XDDFNumericalDataSource<Double> passSrc = XDDFDataSourcesFactory.fromNumericCellRange(
                    sheet, new CellRangeAddress(2, lastRow, numCols - 4, numCols - 4)
            );
            XDDFLineChartData.Series passSeries = (XDDFLineChartData.Series) lineData.addSeries(xNames, passSrc);
            passSeries.setTitle("合格率", null);

            XDDFNumericalDataSource<Double> excelSrc = XDDFDataSourcesFactory.fromNumericCellRange(
                    sheet, new CellRangeAddress(2, lastRow, numCols - 1, numCols - 1)
            );
            XDDFLineChartData.Series excelSeries = (XDDFLineChartData.Series) lineData.addSeries(xNames, excelSrc);
            excelSeries.setTitle("班级前30学生占年级前30学生占比", null);

            lineChart.plot(lineData);

            CTPlotArea linePlot = lineChart.getCTChart().getPlotArea();
            if (linePlot.sizeOfLineChartArray() > 0) {
                for (CTLineSer ls : linePlot.getLineChartArray(0).getSerList()) {
                    ls.addNewDLbls().addNewShowVal().setVal(true);
                    ls.getDLbls().addNewShowLegendKey().setVal(false);
                    ls.getDLbls().addNewShowCatName().setVal(false);
                    ls.getDLbls().addNewShowSerName().setVal(false);
                }
            }


            processStudentSheets(workbook, classStatisticData, 2);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            String filename = IdUtil.fastSimpleUUID() + ".xlsx";
            return fileService.saveFile(out.toByteArray(), filename);
        } catch (Exception e) {
            throw new RuntimeException("生成Excel失败", e);
        }
    }


//    private void setLineMarkerColor(XDDFLineChartData.Series series, String hexColor) throws Exception {
//        // 将十六进制颜色转换为RGB字节数组
//        byte[] rgb = hex2Rgb(hexColor);
//
//        // 获取底层CT对象
//        //TODO:等待poi库升级到5.2.3版本
//        CTLineSer ctLineSer = series.getCTLineSer();
//
//        // 创建标记对象
//        CTMarker marker = ctLineSer.addNewMarker();
//        marker.addNewSymbol().setVal(STMarkerStyle.DIAMOND); // 设置标记形状
//
//        // 设置标记颜色（填充+边框）
//        CTShapeProperties markerProp = marker.addNewSpPr();
//
//        // 填充颜色
//        CTSRgbColor fillColor = markerProp.addNewSolidFill().addNewSrgbClr();
//        fillColor.setVal(rgb);
//
//        // 边框颜色（与填充色相同）
//        CTSRgbColor lineColor = markerProp.addNewLn().addNewSolidFill().addNewSrgbClr();
//        lineColor.setVal(rgb);
//    }

    private void lineSeriesColor(XDDFChartData.Series series, XDDFColor color) {
        XDDFSolidFillProperties fill = new XDDFSolidFillProperties(color);
        XDDFLineProperties line = new XDDFLineProperties();
        line.setFillProperties(fill);
        XDDFShapeProperties properties = series.getShapeProperties();
        if (properties == null) {
            properties = new XDDFShapeProperties();
        }
        properties.setLineProperties(line);
        series.setShapeProperties(properties);
    }

    private byte[] hex2Rgb(String colorStr) {
        int r = Integer.valueOf(colorStr.substring(1, 3), 16);
        int g = Integer.valueOf(colorStr.substring(3, 5), 16);
        int b = Integer.valueOf(colorStr.substring(5, 7), 16);
        return new byte[]{(byte) r, (byte) g, (byte) b};
    }

    @Override
    public JSONArray getClassStatisticDataWithoutRanges(ClassStatisticDataReq req) {
        JSONArray res = new JSONArray();
        JSONArray classStatisticData = new JSONArray();
        JSONArray studentRows = new JSONArray();
        LinkedHashSet<String> globalTypeOrder = new LinkedHashSet<>();

        for (ClassStatisticDataItem classStatisticDataItem : req.getFileDetails()) {
            List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                    .eq(DocCorrectTask::getDeleted, 0)
                    .eq(DocCorrectTask::getFileId, classStatisticDataItem.getFileId()));

            List<String> names = new ArrayList<>();
            List<String> studentNumbers = new ArrayList<>();
            List<String> docNames = new ArrayList<>();
            JSONArray studentScores = new JSONArray();
            Integer studentCount = 0;

            // 第一次遍历：收集分数类型顺序
            List<List<DocCorrectRecordDTO>> recordsList = new ArrayList<>();
            List<DocCorrectConfig> docCorrectConfigs = new ArrayList<>();
            for (DocCorrectTask docCorrectTask : docCorrectTasks) {
                List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(docCorrectTask.getId(), null);
                DocCorrectConfig config = docCorrectConfigService.getById(docCorrectTask.getConfigId());
                docCorrectConfigs.add(config);
                List<DocCorrectRecordDTO> recordDTOS = new ArrayList<>();
                for (DocCorrectRecord record : records) {
                    DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
                    dto.setConfig(BeanUtil.copyProperties(config, DocCorrectConfigDTO.class));
                    recordDTOS.add(dto);
                    JSONObject scoreTypeMap = dto.getScore().getJSONObject("scoreTypeMap");
                    if (scoreTypeMap != null) {
                        globalTypeOrder.addAll(scoreTypeMap.keySet());
                    }
                }
                recordsList.add(recordDTOS);
            }

            List<String> typeOrder = new ArrayList<>(globalTypeOrder);
            Map<String, BigDecimal> aggregateScored = new HashMap<>();
            Integer hege = 0;
            Integer youxiu = 0;
            BigDecimal sumTotalScore = BigDecimal.ZERO;
            studentCount = recordsList.get(0).size();

            // 第二次遍历：处理实际数据
            for (List<DocCorrectRecordDTO> recordDTOS : recordsList) {
                int idx = 0;
                for (DocCorrectRecordDTO docCorrectRecordDTO : recordDTOS) {
                    JSONObject score = docCorrectRecordDTO.getScore();

                    if (studentScores.size() < studentCount) {
                        studentScores.put(score);
                        names.add(docCorrectRecordDTO.getIdentify());
                        studentNumbers.add(docCorrectRecordDTO.getStudentNumber());
                        docNames.add(docCorrectRecordDTO.getDocname());
                    } else {
                        studentScores.put(idx, docCorrectRecordDTO.mergeScore(score, studentScores.getJSONObject(idx)));
                    }

                    idx++;
                }
            }

            // 计算总分和类型分数
            for (int i = 0; i < studentScores.size(); i++) {
                JSONObject score = studentScores.getJSONObject(i);
                BigDecimal totalScore = score.getBigDecimal("scored");
                sumTotalScore = sumTotalScore.add(totalScore);

                if (totalScore.compareTo(BigDecimal.valueOf(req.getHegeScore())) >= 0) hege++;
                if (totalScore.compareTo(BigDecimal.valueOf(req.getYouxiuScore())) >= 0) youxiu++;

                JSONObject scoreTypeMap = score.getJSONObject("scoreTypeMap");
                if (scoreTypeMap != null) {
                    for (String type : typeOrder) {
                        JSONObject typeObj = scoreTypeMap.getJSONObject(type);
                        if (typeObj != null) {
                            BigDecimal typeScored = typeObj.getBigDecimal("scored", BigDecimal.ZERO);
                            aggregateScored.put(type, aggregateScored.getOrDefault(type, BigDecimal.ZERO).add(typeScored));
                        }
                    }
                }
            }

            // 构建班级统计行
            JSONArray row = new JSONArray();
            row.put(classStatisticDataItem.getClassName());
            row.put(classStatisticDataItem.getName());
            for (String type : typeOrder) {
                BigDecimal avg = aggregateScored.getOrDefault(type, BigDecimal.ZERO)
                        .divide(BigDecimal.valueOf(studentCount), 2, RoundingMode.HALF_UP);
                row.put(avg);
            }
            row.put(sumTotalScore.divide(BigDecimal.valueOf(studentCount), 2, RoundingMode.HALF_UP));
            row.put(String.format("%d/%d", hege, studentCount));
            row.put(String.format("%d/%d", youxiu, studentCount));

            if (classStatisticData.isEmpty()) {
                JSONArray header = new JSONArray().put("班级").put("试卷名称");
                header.addAll(typeOrder);
                header.add("总分");
                header.add("合格率");
                header.add("优秀率");
                classStatisticData.put(header);
            }
            classStatisticData.put(row);

            // 构建学生详细数据（包含平均分）
            JSONArray studentTable = new JSONArray();
            JSONArray studentHeader = new JSONArray().put("序号").put("学号").put("姓名");
            studentHeader.addAll(typeOrder);
            studentHeader.add("总分");
            studentTable.put(studentHeader);

            // 初始化分数总和
            Map<String, BigDecimal> typeSums = new HashMap<>();
            BigDecimal totalSum = BigDecimal.ZERO;

            // 填充学生数据行
            for (int i = 0; i < studentScores.size(); i++) {
                JSONArray studentRow = new JSONArray();
                String[] docParts = docNames.get(i).split("_");
                studentRow.put(docParts[docParts.length - 1]);
                studentRow.put(studentNumbers.get(i));
                studentRow.put(names.get(i));

                JSONObject scoreData = studentScores.getJSONObject(i);
                JSONObject scoreTypeMap = scoreData.getJSONObject("scoreTypeMap");

                for (String type : typeOrder) {
                    BigDecimal score = (scoreTypeMap != null && scoreTypeMap.containsKey(type))
                            ? scoreTypeMap.getJSONObject(type).getBigDecimal("scored", BigDecimal.ZERO)
                            : BigDecimal.ZERO;
                    studentRow.put(score.setScale(1, RoundingMode.HALF_UP));
                    typeSums.put(type, typeSums.getOrDefault(type, BigDecimal.ZERO).add(score));
                }

                BigDecimal totalScore = scoreData.getBigDecimal("scored", BigDecimal.ZERO);
                studentRow.put(totalScore.setScale(1, RoundingMode.HALF_UP));
                totalSum = totalSum.add(totalScore);
                studentTable.put(studentRow);
            }

            // 按照学号排序
            if (Boolean.TRUE.equals(req.getOrderByStudentNumber())) {
                // studentTable是JSONArray 排除第一行表头进行排序
                sortStudentTable(studentTable);
            }

            // 添加平均分数据行
            if (studentRows.size() > 1) {
                JSONArray avgRow = new JSONArray();
                avgRow.put("平均分");
                avgRow.put("");
                avgRow.put("");

                for (String type : typeOrder) {
                    BigDecimal avg = typeSums.getOrDefault(type, BigDecimal.ZERO)
                            .divide(BigDecimal.valueOf(studentScores.size()), 2, RoundingMode.HALF_UP)
                            .setScale(1, RoundingMode.HALF_UP);
                    avgRow.put(avg);
                }

                BigDecimal totalAvg = totalSum.divide(BigDecimal.valueOf(studentScores.size()), 2, RoundingMode.HALF_UP)
                        .setScale(1, RoundingMode.HALF_UP);
                avgRow.put(totalAvg);
                studentTable.put(avgRow);
            }


            studentRows.put(studentTable);
        }


        res.add(classStatisticData);
        res.addAll(studentRows);
        return res;
    }

    // 新增方法：处理学生详情Sheet
    private void processStudentSheets(XSSFWorkbook workbook, JSONArray classStatisticData,int classIndex) {
        // 从第二个元素开始是学生数据（index 1开始）
        for (int i = 1; i < classStatisticData.size(); i++, classIndex++) {
            JSONArray classStudents = classStatisticData.getJSONArray(i);
            if (classStudents.isEmpty()) continue;

            // 获取对应的班级名称（从统计数据中获取）
            String className = getClassNameFromStats(classStatisticData, classIndex);

            // 创建学生Sheet（自动处理名称有效性）
            XSSFSheet studentSheet = createStudentSheet(workbook, className);

            // 写入学生数据
            writeStudentData(studentSheet, classStudents);
        }
    }

    // 从统计数据获取班级名称
    private String getClassNameFromStats(JSONArray classStatisticData, int classIndex) {
        try {
            JSONArray stats = classStatisticData.getJSONArray(0);
            if (stats.size() > classIndex) {
                return stats.getJSONArray(classIndex).getStr(0);
            }
            return "班级_" + (classIndex);
        } catch (Exception e) {
            return "班级_" + (classIndex);
        }
    }

    // 创建学生Sheet（处理名称有效性）
    private XSSFSheet createStudentSheet(XSSFWorkbook workbook, String originalName) {
//        String baseName = originalName
//                .replaceAll("[\\\\/:*?\$$\$$]", "") // 去除非法字符
//                .substring(0, Math.min(originalName.length(), 31)); // 截断到31字符
        String baseName = originalName
                .replaceAll("[\\\\/:*?\"<>|]", "")
                .substring(0, Math.min(originalName.length(), 31));

        // 处理重复名称
        String finalName = baseName;
        int counter = 1;
        while (workbook.getSheet(finalName) != null) {
            finalName = baseName.substring(0, Math.min(baseName.length(), 28))
                    + "_" + (counter++);
        }
        return workbook.createSheet(finalName);
    }


    private void writeStudentData(XSSFSheet sheet, JSONArray students) {
        // 表头处理（第一个元素是表头数组）
        JSONArray header = students.getJSONArray(0);
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < header.size(); i++) {
            headerRow.createCell(i).setCellValue(header.getStr(i));
        }

        // 数据行处理（从第二个元素开始）
        for (int rowIdx = 1; rowIdx < students.size(); rowIdx++) {
            JSONArray rowData = students.getJSONArray(rowIdx);
            Row row = sheet.createRow(rowIdx);
            for (int colIdx = 0; colIdx < rowData.size(); colIdx++) {
                Object value = rowData.get(colIdx);
                Cell cell = row.createCell(colIdx);
                if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else {
                    String displayValue = Optional.ofNullable(value)
                            .map(Object::toString)
                            .orElse("N/A"); // 空值显示为N/A
                    cell.setCellValue(displayValue);
                }
            }
        }

        // ------------------------- 列宽设置（完全重构） -------------------------
        for (int colIdx = 0; colIdx < header.size(); colIdx++) {
            String colName = header.getStr(colIdx);

            // 精准匹配关键列
            if ("学号".equals(colName)) {
                sheet.setColumnWidth(colIdx, 10 * 256); // 学号列：10字符
            } else if ("姓名".equals(colName)) {
                sheet.setColumnWidth(colIdx, 10 * 256);  // 姓名列：10字符
            }
            // 动态匹配分数/等级列（支持中文/英文后缀）
            else if (colName.matches(".*(分数|Score)$")) {
                sheet.setColumnWidth(colIdx, 10 * 256);  // 分数列：10字符
            } else if (colName.matches(".*(等级|Grade)$")) {
                sheet.setColumnWidth(colIdx, 10 * 256);  // 等级列：10字符
            } else if (colName.matches(".*(总分|Grade)$")) {
                sheet.setColumnWidth(colIdx, 10 * 256);  // 等级列：10字符
            }
            //序号列保持自动调整（可选）
            else if ("序号".equals(colName)) {
//                 sheet.autoSizeColumn(colIdx);
                sheet.setColumnWidth(colIdx, 10 * 256);
            }
            //无等级数据时没有"分数字段"，设置为10字符
            else {
                sheet.setColumnWidth(colIdx, 10 * 256);
            }
        }
    }


    @Override
    public JSONArray processClassSheet(ClassStatisticDataReq req, JSONArray originalData) {
        // Step 1: 构建新表头
        JSONArray newHeader = new JSONArray();
        newHeader.addAll(Arrays.asList(
                "班级", "教师", "实际人数","实考人数", "总平均分", "合格率",
                "后" + req.getStudentNumberLimit() + "平均分", "后" + req.getStudentNumberLimit() + "合格率", "班级前30学生占年级前30学生占比"
        ));

        // Step 1.1: 构建大标题行（合并单元格格式）
        JSONArray titleRow = new JSONArray();
        String headerTitle = req.getGradeName() + req.getSubject() + "学科学业水平调研数据统计";
        titleRow.put(headerTitle);

        // Step 2: 准备年级总人数（用于计算占比）、每个学生的分数
        int typeCount;

        if (Objects.nonNull(req.getRanges())) {
            typeCount = (originalData.getJSONArray(1).getJSONArray(0).size() - 3) / 2;
        } else {
            typeCount = originalData.getJSONArray(1).getJSONArray(0).size() - 3;
        }

        List<Integer> studentCount = calculateTotalStudents(originalData);
        List<List<BigDecimal>> allStudentScores = getAllStudentScores(originalData, studentCount, typeCount);

        // Step 3: 重构数据行
        JSONArray newRows = new JSONArray();
        newRows.put(titleRow);  // 添加合并标题行
        newRows.put(newHeader); // 添加新表头

        List<ClassStatisticDataItem> temp = req.getFileDetails();

        for (int i = 1; i < originalData.size(); i++) {
            String originalClassName = temp.get(i - 1).getClassName();
            String className = extractClassNumber(originalClassName);
            String teacher = temp.get(i - 1).getTeacherName();
            if (StrUtil.isEmpty(teacher)) {
                teacher = "无";
            }
            // 新增实际人数获取逻辑
            int defaultRealNum = studentCount.get(i - 1);
            int dbCount = 0;
            String classId = temp.get(i - 1).getClassId();
            if (StrUtil.isNotBlank(classId)) {
                try {
                    dbCount = userMapper.countDistinctNicknameByClassId(classId);
                } catch (Exception e) {
                    log.error("查询数据库实际人数失败 classId:{}", classId, e);
                }
            }
            int realClassNum = (StrUtil.isNotBlank(classId)) ? Math.max(defaultRealNum, dbCount) : defaultRealNum;

            Integer reqNum = req.getRealClassNum();
            if (reqNum != null && reqNum >= realClassNum) {
                realClassNum = Math.max(reqNum, realClassNum);
            }

            BigDecimal passThreshold = null;
            try {
                passThreshold = BigDecimal.valueOf(req.getHegeScore());
            } catch (Exception e) {
                log.error("合格分数不能为空");
            }
            JSONArray newRow = rebuildDataRow(passThreshold, allStudentScores.get(i - 1), allStudentScores, studentCount.get(i - 1), className, teacher,  realClassNum, i -1);
            newRows.put(newRow);
        }

        return newRows;
    }

    private String extractClassNumber(String original) {
        if (StrUtil.isBlank(original)) {
            return "无";
        }

        // 使用正则表达式查找所有连续3位数字
        Pattern pattern = Pattern.compile("\\d{3}");
        Matcher matcher = pattern.matcher(original);

        String lastMatch = null;
        while (matcher.find()) {
            lastMatch = matcher.group();
        }

        // 确保返回字符串，避免被转为数值
        return lastMatch != null ? lastMatch : original;
    }
    /**
     * 获取年纪所有学生的分数
     *
     * @param originalData
     * @param studentCount
     * @param typeCount
     * @return
     */
    private List<List<BigDecimal>> getAllStudentScores(JSONArray originalData, List<Integer> studentCount, Integer typeCount) {
        List<List<BigDecimal>> scores = new ArrayList<>();
        BigDecimal totalScore = BigDecimal.ZERO;
        for (int i = 1; i < originalData.size(); i++) {
            List<BigDecimal> scoreSheet = new ArrayList<>();
            JSONArray sheet = originalData.getJSONArray(i);
            for (int j = 0; j < studentCount.get(i - 1); j++) {
                BigDecimal score = sheet.getJSONArray(j + 1).getBigDecimal(2 + typeCount);
                if (score != null) {
                    totalScore = totalScore.add(score);
                    scoreSheet.add(score);
                }
                //totalScore = totalScore.add(score);
            }
            scores.add(scoreSheet);
        }
        return scores;
    }

    private List<Integer> calculateTotalStudents(JSONArray originalData) {
        List<Integer> students = new ArrayList<>();
        JSONArray sheet = originalData.getJSONArray(0);
        for (int i = 1; i < sheet.size(); i++) {
            JSONArray row = sheet.getJSONArray(i);
            // 每一行的最后是优秀率
            String youxiuLv = row.getStr(row.size() - 1);
            int studentNumber = 0;
            try {
                studentNumber = Integer.parseInt(youxiuLv.split("/")[1]);
            } catch (Exception e) {
                log.error("计算学生总数量报错：{}", e.getMessage());
            }
            students.add(studentNumber);
        }
        return students;
    }

    private JSONArray rebuildDataRow(BigDecimal passThreshold, List<BigDecimal> scores, List<List<BigDecimal>> allStudentScores, int classStudentSize, String className, String teacher,int  RealclassStudentSize, int classIndex) {
        JSONArray newRow = new JSONArray();

        newRow.put(className);
        newRow.put(teacher);
        //todo:计算实际人数
        newRow.put(RealclassStudentSize);
//        newRow.put(classStudentSize);
        newRow.put(classStudentSize);

        // 计算所有学生的总分
        BigDecimal totalScore = scores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        // 保留两位小数
        BigDecimal divisor = BigDecimal.valueOf(classStudentSize);
        BigDecimal totalAvg = totalScore.divide(divisor, 2, RoundingMode.HALF_UP);
        newRow.put(totalAvg);

        // Step 4: 合格率计算（兼容 double 类型阈值）
        long passCount = scores.stream()
                .filter(score -> score != null && score.compareTo(passThreshold) >= 0)
                .count();
        BigDecimal passRate = (divisor.compareTo(BigDecimal.ZERO) == 0)
                ? BigDecimal.ZERO
                : BigDecimal.valueOf(passCount).multiply(BigDecimal.valueOf(100))
                .divide(divisor, 2, RoundingMode.HALF_UP);
        String passRateStr = passRate.toPlainString() + "%";
        newRow.put(passRateStr);

        // 先对 scores 按分数升序排序，null视为最低分（可根据需求调整）
        List<BigDecimal> sortedScores = scores.stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());

        // 取最低30名
        int count = sortedScores.size();
        int startIndex = Math.max(0, count - 30);
        List<BigDecimal> last30Scores = sortedScores.subList(startIndex, count);
        // 计算后30名平均分
        BigDecimal sumLast30 = last30Scores.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal divisorLast30 = BigDecimal.valueOf(last30Scores.size());
        BigDecimal avgLast30 = divisorLast30.compareTo(BigDecimal.ZERO) == 0
                ? BigDecimal.ZERO
                : sumLast30.divide(divisorLast30, 2, RoundingMode.HALF_UP);
        // 计算后30名合格人数
        long passCountLast30 = last30Scores.stream()
                .filter(score -> score.compareTo(passThreshold) >= 0)
                .count();
        // 计算后30名合格率
        BigDecimal passRateLast30 = divisorLast30.compareTo(BigDecimal.ZERO) == 0
                ? BigDecimal.ZERO
                : BigDecimal.valueOf(passCountLast30).multiply(BigDecimal.valueOf(100))
                .divide(divisorLast30, 2, RoundingMode.HALF_UP);
        // 输出格式，如需格式化为"xx.xx%"
        String avgLast30Str = avgLast30.toPlainString();
        String passRateLast30Str = passRateLast30.toPlainString() + "%";
        // 添加到结果行或打印
        newRow.put(avgLast30Str);
        newRow.put(passRateLast30Str);

        // Step 6: 前30占比
        newRow.put(computeStrictTop30Share(classIndex, allStudentScores));

        return newRow;
    }

    /**
     * 计算班级前30%学生中，进入年级前30%学生的占比（百分比字符串）
     * @param classScores 班级学生成绩列表
     * @param allStudentScores 年级所有班级学生成绩列表（二维列表）
     * @return 占比百分比字符串，如 "75.00%"
     */
    public static String computeClassTop30PercentInGradeTop30PercentRatio(
            List<BigDecimal> classScores,
            List<List<BigDecimal>> allStudentScores) {

        // 合并所有年级成绩并降序排序
        List<BigDecimal> allGradeScores = allStudentScores.stream()
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());

        int totalStudents = allGradeScores.size();
        if (totalStudents == 0) return "0.00%";

        // 确定年级前30%人数（向上取整）
        int gradeTop30Count = (int) Math.ceil(totalStudents * 0.3);

        // 获取年级前30%的分数线（处理并列分数）
        BigDecimal cutoff = allGradeScores.get(gradeTop30Count - 1);

        // 统计本班进入年级前30%的人数
        long classInGradeTop30 = classScores.stream()
                .filter(score -> score != null && score.compareTo(cutoff) >= 0)
                .count();

        // 计算占比（分母为年级前30%总人数）
        BigDecimal ratio = BigDecimal.valueOf(classInGradeTop30)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(gradeTop30Count), 2, RoundingMode.HALF_UP);

        return ratio.toPlainString() + "%";
    }

    /**
     * 严格控制全级与指定班级各前30名，计算该班在全级前30名中的占比。
     * 内部基于"班级下标+班内序号"生成虚拟学生ID，以保证唯一性。
     *
     * @param classIndex       目标班级在 allStudentScores 中的下标（0 起）
     * @param allStudentScores 年级所有班级学生分数列表（并行结构）
     * @return 占比百分比字符串，如 "23.33%"
     * @throws IllegalArgumentException 输入校验失败时抛出
     */
    public static String computeStrictTop30Share(
            int classIndex,
            List<List<BigDecimal>> allStudentScores) {

        // 参数校验
        if (allStudentScores == null || allStudentScores.isEmpty()) {
            throw new IllegalArgumentException("allStudentScores 必须非空");
        }
        if (classIndex < 0 || classIndex >= allStudentScores.size()) {
            throw new IllegalArgumentException(
                    String.format("classIndex 越界，应在 [0, %d)，但传入 %d",
                            allStudentScores.size(), classIndex));
        }

        // 1. 全级成绩及虚拟ID收集并降序排序
        List<Map.Entry<String, BigDecimal>> allEntries = new ArrayList<>();
        for (int ci = 0; ci < allStudentScores.size(); ci++) {
            List<BigDecimal> scores = allStudentScores.get(ci);
            for (int sj = 0; sj < scores.size(); sj++) {
                BigDecimal sc = scores.get(sj);
                if (sc != null) {
                    String vid = String.format("C%d_S%d", ci, sj);
                    allEntries.add(new AbstractMap.SimpleEntry<>(vid, sc));
                }
            }
        }
        allEntries.sort(Comparator
                .comparing(Map.Entry<String, BigDecimal>::getValue)
                .reversed());

        int globalLimit = Math.min(30, allEntries.size());
        Set<String> globalTop30Ids = allEntries
                .subList(0, globalLimit)
                .stream()
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 2. 指定班级成绩及虚拟ID收集并降序排序
        List<Map.Entry<String, BigDecimal>> classEntries = new ArrayList<>();
        List<BigDecimal> targetScores = allStudentScores.get(classIndex);
        for (int sj = 0; sj < targetScores.size(); sj++) {
            BigDecimal sc = targetScores.get(sj);
            if (sc != null) {
                String vid = String.format("C%d_S%d", classIndex, sj);
                classEntries.add(new AbstractMap.SimpleEntry<>(vid, sc));
            }
        }
        classEntries.sort(Comparator
                .comparing(Map.Entry<String, BigDecimal>::getValue)
                .reversed());

        int classLimit = Math.min(30, classEntries.size());
        List<String> classTop30Ids = classEntries
                .subList(0, classLimit)
                .stream()
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 3. 交集计数（同一虚拟ID即同一人）
        long matchCount = classTop30Ids.stream()
                .filter(globalTop30Ids::contains)
                .count();

        // 4. 计算占比并格式化输出
        BigDecimal ratio = BigDecimal.valueOf(matchCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(globalLimit), 2, RoundingMode.HALF_UP);

        return ratio.toPlainString() + "%";
    }

    @Override
    public String generateClassStatisticExcel5(JSONArray classStatisticData) {
        // 向后兼容的方法，创建一个空的ClassStatisticDataReq
        ClassStatisticDataReq req = new ClassStatisticDataReq();
        return generateClassStatisticExcel5(classStatisticData, req);
    }

    @Override
    public String generateClassStatisticExcel5(JSONArray classStatisticData, ClassStatisticDataReq req) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("班级统计");
            // 样式五只处理[header, row, row, ...]格式，无需title
            JSONArray headerArr = classStatisticData.getJSONArray(0);
            int numCols = headerArr.size();

            // 计算各科目得分率数据
            JSONArray scoreRateData = calculateScoreRateData(classStatisticData, req);

            // 计算年级汇总数据
            JSONArray gradeSummaryRow = calculateGradeSummaryRow(classStatisticData);

            // 通用边框样式（细黑边框）
            BorderStyle thinBorder = BorderStyle.THIN;
            short black = IndexedColors.BLACK.getIndex();

            // 标题样式（居中、加粗、微软雅黑、字号18）
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setFontName("微软雅黑");
            titleFont.setFontHeightInPoints((short)18);
            titleFont.setColor(IndexedColors.BLACK.getIndex());
            titleFont.setBold(true);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 表头样式（左对齐）
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setColor(IndexedColors.BLACK.getIndex());
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.LEFT);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setBorderTop(thinBorder);
            headerStyle.setBorderBottom(thinBorder);
            headerStyle.setBorderLeft(thinBorder);
            headerStyle.setBorderRight(thinBorder);
            headerStyle.setTopBorderColor(black);
            headerStyle.setBottomBorderColor(black);
            headerStyle.setLeftBorderColor(black);
            headerStyle.setRightBorderColor(black);

            // 百分比样式
            DataFormat fmt = workbook.createDataFormat();
            CellStyle percentStyle = workbook.createCellStyle();
            percentStyle.setDataFormat(fmt.getFormat("0.0%"));
            percentStyle.setAlignment(HorizontalAlignment.LEFT);
            percentStyle.setBorderTop(thinBorder);
            percentStyle.setBorderBottom(thinBorder);
            percentStyle.setBorderLeft(thinBorder);
            percentStyle.setBorderRight(thinBorder);
            percentStyle.setTopBorderColor(black);
            percentStyle.setBottomBorderColor(black);
            percentStyle.setLeftBorderColor(black);
            percentStyle.setRightBorderColor(black);

            // 数值样式
            CellStyle decimalStyle = workbook.createCellStyle();
            decimalStyle.setDataFormat(fmt.getFormat("0.0"));
            decimalStyle.setAlignment(HorizontalAlignment.LEFT);
            decimalStyle.setBorderTop(thinBorder);
            decimalStyle.setBorderBottom(thinBorder);
            decimalStyle.setBorderLeft(thinBorder);
            decimalStyle.setBorderRight(thinBorder);
            decimalStyle.setTopBorderColor(black);
            decimalStyle.setBottomBorderColor(black);
            decimalStyle.setLeftBorderColor(black);
            decimalStyle.setRightBorderColor(black);

            // 整数样式
            CellStyle integerStyle = workbook.createCellStyle();
            integerStyle.setDataFormat(fmt.getFormat("0"));
            integerStyle.setAlignment(HorizontalAlignment.LEFT);
            integerStyle.setBorderTop(thinBorder);
            integerStyle.setBorderBottom(thinBorder);
            integerStyle.setBorderLeft(thinBorder);
            integerStyle.setBorderRight(thinBorder);
            integerStyle.setTopBorderColor(black);
            integerStyle.setBottomBorderColor(black);
            integerStyle.setLeftBorderColor(black);
            integerStyle.setRightBorderColor(black);

            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setAlignment(HorizontalAlignment.LEFT);
            textStyle.setBorderTop(thinBorder);
            textStyle.setBorderBottom(thinBorder);
            textStyle.setBorderLeft(thinBorder);
            textStyle.setBorderRight(thinBorder);
            textStyle.setTopBorderColor(black);
            textStyle.setBottomBorderColor(black);
            textStyle.setLeftBorderColor(black);
            textStyle.setRightBorderColor(black);

            // 1. 标题行（动态生成标题）
            Row titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            
            // 从req中获取年级名称和科目名称，如果为空则使用默认值
            String gradeName = (req != null && StrUtil.isNotBlank(req.getGradeName())) ? req.getGradeName() : "六年级";
            String subject = (req != null && StrUtil.isNotBlank(req.getSubject())) ? req.getSubject() : "数学";
            String titleText = gradeName + subject + "期末成绩分析汇总表";
            
            titleCell.setCellValue(titleText);
            titleCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, numCols - 1));

            // 2. 表头行
            CellStyle centeredHeaderStyle = workbook.createCellStyle();
            centeredHeaderStyle.cloneStyleFrom(headerStyle);
            centeredHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
            centeredHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            Row headerRow1 = sheet.createRow(1);
            headerRow1.setHeightInPoints(25);
            Row headerRow2 = sheet.createRow(2);
            headerRow2.setHeightInPoints(25);

            // Create all cells with style to have borders
            for (int i = 0; i < numCols; i++) {
                headerRow1.createCell(i).setCellStyle(centeredHeaderStyle);
                headerRow2.createCell(i).setCellStyle(centeredHeaderStyle);
            }

            // Set header values and merge cells
            headerRow1.getCell(0).setCellValue("班级");
            sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
            headerRow1.getCell(1).setCellValue("总分");
            sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));
            headerRow1.getCell(2).setCellValue("平均分");
            sheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2));
            headerRow1.getCell(3).setCellValue("应考人数");
            sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3));
            headerRow1.getCell(4).setCellValue("实考人数");
            sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4));

            headerRow1.getCell(5).setCellValue("优秀");
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 6));
            headerRow2.getCell(5).setCellValue("人数");
            headerRow2.getCell(6).setCellValue("%");

            headerRow1.getCell(7).setCellValue("良好");
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 7, 8));
            headerRow2.getCell(7).setCellValue("人数");
            headerRow2.getCell(8).setCellValue("%");

            headerRow1.getCell(9).setCellValue("合格");
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 9, 10));
            headerRow2.getCell(9).setCellValue("人数");
            headerRow2.getCell(10).setCellValue("%");

            headerRow1.getCell(11).setCellValue("待合格");
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 11, 12));
            headerRow2.getCell(11).setCellValue("人数");
            headerRow2.getCell(12).setCellValue("%");

            headerRow1.getCell(13).setCellValue("执教老师");
            sheet.addMergedRegion(new CellRangeAddress(1, 2, 13, 13));

            // 3. 数据行
            for (int r = 1; r < classStatisticData.size(); r++) {
                JSONArray dataArr = classStatisticData.getJSONArray(r);
                Row row = sheet.createRow(r + 2); // 数据行从第4行开始
                for (int c = 0; c < numCols; c++) {
                    Cell cell = row.createCell(c);
                    String text = dataArr.getStr(c);
                    String headerName = headerArr.getStr(c);
                    if (c == 0) {
                        cell.setCellType(CellType.STRING);
                        cell.setCellValue(text);
                        cell.setCellStyle(textStyle);
                        continue;
                    }
                    // 判断列名
                    if ("平均分".equals(headerName)) {
                        // 平均分保留两位小数
                        if (text != null) {
                            text = text.trim();
                            try {
                                double d = Double.parseDouble(text);
                                cell.setCellValue(Math.round(d * 10.0) / 10.0); // 保留一位小数
                                cell.setCellStyle(decimalStyle);
                            } catch (NumberFormatException e) {
                                cell.setCellValue(text);
                                cell.setCellStyle(textStyle);
                            }
                        } else {
                            cell.setBlank();
                            cell.setCellStyle(textStyle);
                        }
                        continue;
                    }
                    if ("应考人数".equals(headerName) || "实考人数".equals(headerName) || "优秀人数".equals(headerName) || "良好人数".equals(headerName) || "合格人数".equals(headerName) || "待合格人数".equals(headerName)) {
                        // 这些列保留整数
                        if (text != null) {
                            text = text.trim();
                            try {
                                double d = Double.parseDouble(text);
                                cell.setCellValue((int) d);
                                cell.setCellStyle(integerStyle);
                            } catch (NumberFormatException e) {
                                cell.setCellValue(text);
                                cell.setCellStyle(textStyle);
                            }
                        } else {
                            cell.setBlank();
                            cell.setCellStyle(textStyle);
                        }
                        continue;
                    }
                    if (text != null) {
                        text = text.trim();
                        try {
                            if (text.endsWith("%")) {
                                String num = text.substring(0, text.length() - 1);
                                double d = Double.parseDouble(num) / 100.0;
                                cell.setCellValue(d);
                                cell.setCellStyle(percentStyle);
                            } else {
                                double d = Double.parseDouble(text);
                                cell.setCellValue(Math.round(d * 10.0) / 10.0); // 保留一位小数
                                cell.setCellStyle(decimalStyle);
                            }
                        } catch (NumberFormatException e) {
                            cell.setCellValue(text);
                            cell.setCellStyle(textStyle);
                        }
                    } else {
                        cell.setBlank();
                        cell.setCellStyle(textStyle);
                    }
                }
            }

            // 4. 添加年级汇总行
            int gradeSummaryRowIndex = classStatisticData.size() + 2;
            Row gradeSummaryRowObj = sheet.createRow(gradeSummaryRowIndex);
            for (int c = 0; c < numCols; c++) {
                Cell cell = gradeSummaryRowObj.createCell(c);
                String text = gradeSummaryRow.getStr(c);
                String headerName = headerArr.getStr(c);
                
                if (c == 0) {
                    cell.setCellType(CellType.STRING);
                    cell.setCellValue(text);
                    cell.setCellStyle(textStyle);
                    continue;
                }
                
                // 判断列名
                if ("平均分".equals(headerName)) {
                    if (text != null) {
                        text = text.trim();
                        try {
                            double d = Double.parseDouble(text);
                            cell.setCellValue(Math.round(d * 10.0) / 10.0);
                            // 为平均分列创建加粗样式
                            CellStyle boldDecimalStyle = workbook.createCellStyle();
                            boldDecimalStyle.cloneStyleFrom(decimalStyle);
                            Font boldFont = workbook.createFont();
                            boldFont.setBold(true);
                            boldDecimalStyle.setFont(boldFont);
                            cell.setCellStyle(boldDecimalStyle);
                        } catch (NumberFormatException e) {
                            cell.setCellValue(text);
                            // 为平均分列创建加粗样式
                            CellStyle boldTextStyle = workbook.createCellStyle();
                            boldTextStyle.cloneStyleFrom(textStyle);
                            Font boldFont = workbook.createFont();
                            boldFont.setBold(true);
                            boldTextStyle.setFont(boldFont);
                            cell.setCellStyle(boldTextStyle);
                        }
                    } else {
                        cell.setBlank();
                        // 为平均分列创建加粗样式
                        CellStyle boldTextStyle = workbook.createCellStyle();
                        boldTextStyle.cloneStyleFrom(textStyle);
                        Font boldFont = workbook.createFont();
                        boldFont.setBold(true);
                        boldTextStyle.setFont(boldFont);
                        cell.setCellStyle(boldTextStyle);
                    }
                    continue;
                }
                
                if ("应考人数".equals(headerName) || "实考人数".equals(headerName) || "优秀人数".equals(headerName) || "良好人数".equals(headerName) || "合格人数".equals(headerName) || "待合格人数".equals(headerName)) {
                    if (text != null) {
                        text = text.trim();
                        try {
                            double d = Double.parseDouble(text);
                            cell.setCellValue((int) d);
                            cell.setCellStyle(integerStyle);
                        } catch (NumberFormatException e) {
                            cell.setCellValue(text);
                            cell.setCellStyle(textStyle);
                        }
                    } else {
                        cell.setBlank();
                        cell.setCellStyle(textStyle);
                    }
                    continue;
                }
                
                if (text != null) {
                    text = text.trim();
                    try {
                        if (text.endsWith("%")) {
                            String num = text.substring(0, text.length() - 1);
                            double d = Double.parseDouble(num) / 100.0;
                            cell.setCellValue(d);
                            cell.setCellStyle(percentStyle);
                        } else {
                            double d = Double.parseDouble(text);
                            cell.setCellValue(Math.round(d * 10.0) / 10.0); // 保留一位小数
                            cell.setCellStyle(decimalStyle);
                        }
                    } catch (NumberFormatException e) {
                        cell.setCellValue(text);
                        cell.setCellStyle(textStyle);
                    }
                } else {
                    cell.setBlank();
                    cell.setCellStyle(textStyle);
                }
            }

            // 调整列宽
            int fixedWidth = 10 * 256;
            for (int i = 0; i < numCols; i++) {
                sheet.setColumnWidth(i, fixedWidth);
            }

            // 5. 添加"各题得分率统计"表格
            if (scoreRateData != null && !scoreRateData.isEmpty()) {
                int lastRow = sheet.getLastRowNum();
                int startRow = lastRow + 3; // 隔一行开始新表格

                // 5.1 新表格标题行
                Row scoreRateTitleRow = sheet.createRow(startRow);
                scoreRateTitleRow.setHeightInPoints(30);
                Cell scoreRateTitleCell = scoreRateTitleRow.createCell(0);
                scoreRateTitleCell.setCellValue(subject + "各题得分率统计");
                scoreRateTitleCell.setCellStyle(titleStyle);

                JSONArray scoreRateHeader = scoreRateData.getJSONArray(0);
                int scoreRateCols = scoreRateHeader.size();

                // 只有当列数大于1时才进行合并
                if (scoreRateCols > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 0, scoreRateCols - 1));
                }

                // 5.2 新表格表头行（两行表头）
                Row scoreRateHeaderRow1 = sheet.createRow(startRow + 1);
                scoreRateHeaderRow1.setHeightInPoints(25);
                Row scoreRateHeaderRow2 = sheet.createRow(startRow + 2);
                scoreRateHeaderRow2.setHeightInPoints(25);

                // 创建所有单元格并设置样式
                for (int i = 0; i < scoreRateCols; i++) {
                    scoreRateHeaderRow1.createCell(i).setCellStyle(centeredHeaderStyle);
                    scoreRateHeaderRow2.createCell(i).setCellStyle(centeredHeaderStyle);
                }

                // 设置第一行表头：班级列和科目名称
                scoreRateHeaderRow1.getCell(0).setCellValue("班级");
                sheet.addMergedRegion(new CellRangeAddress(startRow + 1, startRow + 2, 0, 0)); // 班级列合并两行

                // 计算题型数量（每个题型占两列：总分和得分率）
                int questionTypeCount = (scoreRateCols - 1) / 2; // 减去班级列，除以2得到题型数量
                
                // 设置科目名称（每个科目占两列）
                for (int i = 0; i < questionTypeCount; i++) {
                    int colIndex = 1 + i * 2; // 第1个科目从第1列开始，第2个科目从第3列开始，以此类推
                    if (colIndex + 1 < scoreRateCols) {
                        String subjectName = "";
                        if (i < scoreRateHeader.size() - 1) {
                            String headerName = scoreRateHeader.getStr(colIndex);
                            if (StrUtil.isNotBlank(headerName) && headerName.contains("_总分")) {
                                subjectName = headerName.replace("_总分", "");
                            }
                        }
                        scoreRateHeaderRow1.getCell(colIndex).setCellValue(subjectName);
                        sheet.addMergedRegion(new CellRangeAddress(startRow + 1, startRow + 1, colIndex, colIndex + 1)); // 科目名称合并两列
                    }
                }

                // 设置第二行表头：总分和得分率
                for (int i = 0; i < questionTypeCount; i++) {
                    int colIndex = 1 + i * 2;
                    if (colIndex < scoreRateCols) {
                        scoreRateHeaderRow2.getCell(colIndex).setCellValue("总分");
                    }
                    if (colIndex + 1 < scoreRateCols) {
                        scoreRateHeaderRow2.getCell(colIndex + 1).setCellValue("得分率");
                    }
                }

                // 5.3 新表格数据行
                for (int r = 1; r < scoreRateData.size(); r++) {
                    JSONArray dataArr = scoreRateData.getJSONArray(r);
                    Row row = sheet.createRow(startRow + 2 + r);
                    for (int c = 0; c < scoreRateCols; c++) {
                        Cell cell = row.createCell(c);
                        String text = dataArr.getStr(c);

                        if (c == 0) {
                            // 班级列
                            cell.setCellType(CellType.STRING);
                            cell.setCellValue(text);
                            cell.setCellStyle(textStyle);
                        } else if (c % 2 == 1) {
                            // 奇数列是总分列
                            if (text != null) {
                                text = text.trim();
                                try {
                                    double d = Double.parseDouble(text);
                                    cell.setCellValue(d);
                                    cell.setCellStyle(decimalStyle);
                                } catch (NumberFormatException e) {
                                    cell.setCellValue(text);
                                    cell.setCellStyle(textStyle);
                                }
                            } else {
                                cell.setBlank();
                                cell.setCellStyle(textStyle);
                            }
                        } else {
                            // 偶数列是得分率列
                            if (text != null) {
                                text = text.trim();
                                try {
                                    if (text.endsWith("%")) {
                                        String num = text.substring(0, text.length() - 1);
                                        double d = Double.parseDouble(num) / 100.0;
                                        cell.setCellValue(d);
                                        cell.setCellStyle(percentStyle);
                                    } else {
                                        double d = Double.parseDouble(text);
                                        cell.setCellValue(Math.round(d * 10.0) / 10.0); // 保留一位小数
                                        cell.setCellStyle(decimalStyle);
                                    }
                                } catch (NumberFormatException e) {
                                    cell.setCellValue(text);
                                    cell.setCellStyle(textStyle);
                                }
                            } else {
                                cell.setBlank();
                                cell.setCellStyle(textStyle);
                            }
                        }
                    }
                }

                // 5.4 添加第二个表格的年级汇总行
                JSONArray scoreRateGradeSummaryRow = calculateScoreRateGradeSummaryRow(scoreRateData);
                int scoreRateGradeSummaryRowIndex = startRow + 2 + scoreRateData.size();
                Row scoreRateGradeSummaryRowObj = sheet.createRow(scoreRateGradeSummaryRowIndex);
                
                for (int c = 0; c < scoreRateCols; c++) {
                    Cell cell = scoreRateGradeSummaryRowObj.createCell(c);
                    String text = scoreRateGradeSummaryRow.getStr(c);

                    if (c == 0) {
                        // 班级列
                        cell.setCellType(CellType.STRING);
                        cell.setCellValue(text);
                        cell.setCellStyle(textStyle);
                    } else if (c % 2 == 1) {
                        // 奇数列是总分列
                        if (text != null) {
                            text = text.trim();
                            try {
                                double d = Double.parseDouble(text);
                                cell.setCellValue(d);
                                cell.setCellStyle(decimalStyle);
                            } catch (NumberFormatException e) {
                                cell.setCellValue(text);
                                cell.setCellStyle(textStyle);
                            }
                        } else {
                            cell.setBlank();
                            cell.setCellStyle(textStyle);
                        }
                    } else {
                        // 偶数列是得分率列
                        if (text != null) {
                            text = text.trim();
                            try {
                                if (text.endsWith("%")) {
                                    String num = text.substring(0, text.length() - 1);
                                    double d = Double.parseDouble(num) / 100.0;
                                    cell.setCellValue(d);
                                    cell.setCellStyle(percentStyle);
                                } else {
                                    double d = Double.parseDouble(text);
                                    cell.setCellValue(Math.round(d * 10.0) / 10.0); // 保留一位小数
                                    cell.setCellStyle(decimalStyle);
                                }
                            } catch (NumberFormatException e) {
                                cell.setCellValue(text);
                                cell.setCellStyle(textStyle);
                            }
                        } else {
                            cell.setBlank();
                            cell.setCellStyle(textStyle);
                        }
                    }
                }

                // 5.5 设置新表格列宽
                // 班级列宽度设置为10*256
                sheet.setColumnWidth(0, 10 * 256);
                // 每个科目的"总分"和"得分率"列宽度也设置为10*256
                for (int i = 1; i < scoreRateCols; i++) {
                    sheet.setColumnWidth(i, 10 * 256);
                }
            }

            // 6. 添加表格3：清凉小学第2025学年第2学期数学学科成绩统计一览表
            int lastRow = sheet.getLastRowNum();
            int table3StartRow = lastRow + 3; // 隔两行

            // 6.1 标题行
            Row table3TitleRow = sheet.createRow(table3StartRow);
            table3TitleRow.setHeightInPoints(30);
            Cell table3TitleCell = table3TitleRow.createCell(0);
            table3TitleCell.setCellValue("清凉小学第2025学年第2学期" + subject + "学科成绩统计一览表");
            table3TitleCell.setCellStyle(titleStyle);
            int table3ColCount = 21;
            sheet.addMergedRegion(new CellRangeAddress(table3StartRow, table3StartRow, 0, table3ColCount - 1));

            // 6.2 表头行
            String[] table3Headers = new String[] {"班级", "在籍人数", "实考人数", "总分", "均分", "不及格率", "不及格人数", "100", "90-99", "80-89", "70-79", "60-69", "50-59", "40-49", "40分以下", "最低分", "随班就读人数", "病假", "事假", "执者", "备注"};
            // 新增：分段统计行（在表头上方）
            Row table3SegmentRow = sheet.createRow(table3StartRow + 1);
            table3SegmentRow.setHeightInPoints(25); // 与表头一致
            // 居中样式
            CellStyle segmentCenterStyle = workbook.createCellStyle();
            segmentCenterStyle.cloneStyleFrom(headerStyle);
            segmentCenterStyle.setAlignment(HorizontalAlignment.CENTER);
            segmentCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            for (int i = 0; i < table3ColCount; i++) {
                Cell cell = table3SegmentRow.createCell(i);
                cell.setCellStyle(segmentCenterStyle);
                cell.setCellValue("");
            }
            // 只合并7~15列（100~最低分）
            sheet.addMergedRegion(new CellRangeAddress(table3StartRow + 1, table3StartRow + 1, 7, 15));
            table3SegmentRow.getCell(7).setCellValue("成绩分段统计");

            // 6.3 表头行（下移一行）
            Row table3HeaderRow = sheet.createRow(table3StartRow + 2);
            table3HeaderRow.setHeightInPoints(25); // 与分段统计行一致
            // 新增：为表头设置自动换行样式
            CellStyle wrapHeaderStyle = workbook.createCellStyle();
            wrapHeaderStyle.cloneStyleFrom(headerStyle);
            wrapHeaderStyle.setWrapText(true);
            for (int i = 0; i < table3ColCount; i++) {
                Cell cell = table3HeaderRow.createCell(i);
                // 设置表头内容，7为"不及格人数"，16为"随班就读人数"
                if (i == 6) {
                    cell.setCellValue("不及格\n人数");
                    cell.setCellStyle(wrapHeaderStyle);
                } else if (i == 16) {
                    cell.setCellValue("随班就读\n人数");
                    cell.setCellStyle(wrapHeaderStyle);
                } else {
                    cell.setCellValue(table3Headers[i]);
                    cell.setCellStyle(headerStyle);
                }
                if (i == 16) {
                    sheet.setColumnWidth(i, 15 * 256);
                } else {
                    sheet.setColumnWidth(i, 10 * 256);
                }
            }

            // 新增：表格3"总分"列专用一位小数样式
            CellStyle oneDecimalStyle = workbook.createCellStyle();
            oneDecimalStyle.cloneStyleFrom(decimalStyle);
            oneDecimalStyle.setDataFormat(fmt.getFormat("0.0"));

            // 6.4 数据行（下移一行）
            JSONArray table1Data = classStatisticData; // 表1数据

            // 年级汇总统计变量
            int totalEnrollment = 0, totalActual = 0, totalFail = 0, total100 = 0, total90_99 = 0, total80_89 = 0, total70_79 = 0, total60_69 = 0, total50_59 = 0, total40_49 = 0, totalBelow40 = 0;
            BigDecimal sumAvgScore = BigDecimal.ZERO, sumFailRate = BigDecimal.ZERO;
            BigDecimal minOfMinScore = null;
            int classCount = 0;
            BigDecimal totalSumScore = BigDecimal.ZERO; // 新增：总分合计

            int maxClassRows = (req != null && req.getFileDetails() != null) ? req.getFileDetails().size() : table1Data.size() - 1;
            for (int i = 1; i < table1Data.size() && (i-1) < maxClassRows; i++) {
                JSONArray row = table1Data.getJSONArray(i);
                // 获取分数数据
                String className = row.getStr(0);
                int enrollment = row.getInt(3); // 在籍人数=应考人数
                int actual = row.getInt(4);    // 实考人数
                BigDecimal totalScore = new BigDecimal(row.getStr(1));
                BigDecimal avgScore = new BigDecimal(row.getStr(2));
                // 需要获取该班所有学生分数
                String fileId = null;
                if (req != null && req.getFileDetails() != null && (i-1) < req.getFileDetails().size()) {
                    fileId = req.getFileDetails().get(i-1).getFileId();
                }
//
                // === 表格3分段统计：合并逻辑与表格1完全一致 ===
                List<List<DocCorrectRecordDTO>> recordsList = new ArrayList<>();
                int taskCount = 0, recordCount = 0;
                if (fileId != null) {
                    List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(
                            Wrappers.lambdaQuery(DocCorrectTask.class)
                                    .eq(DocCorrectTask::getDeleted, 0)
                                    .eq(DocCorrectTask::getFileId, fileId)
                    );
                    taskCount = docCorrectTasks.size();
                    for (DocCorrectTask task : docCorrectTasks) {
                        List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(task.getId(), null);
                        recordCount += records.size();
                        DocCorrectConfig config = docCorrectConfigService.getById(task.getConfigId());
                        DocCorrectConfigDTO configDTO = BeanUtil.copyProperties(config, com.chaty.dto.DocCorrectConfigDTO.class);
                        List<DocCorrectRecordDTO> recordDTOS = new ArrayList<>();
                        for (DocCorrectRecord record : records) {
                            DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, com.chaty.dto.DocCorrectRecordDTO.class);
                            dto.setConfig(configDTO);
                            recordDTOS.add(dto);
                        }
                        recordsList.add(recordDTOS);
                    }
                }
// 用下标合并每个学生的所有任务分数
                List<JSONObject> mergedScores = new ArrayList<>();
                int studentCount = recordsList.isEmpty() ? 0 : recordsList.get(0).size();
                for (int idx = 0; idx < studentCount; idx++) {
                    JSONObject merged = null;
                    for (List<DocCorrectRecordDTO> recordDTOS : recordsList) {
                        if (idx >= recordDTOS.size()) continue; // 防止越界
                        DocCorrectRecordDTO dto = recordDTOS.get(idx);
                        JSONObject score = dto.getScore();
                        if (merged == null) {
                            merged = score;
                        } else {
                            merged = dto.mergeScore(score, merged);
                        }
                    }
                    if (merged != null) {
                        mergedScores.add(merged);
                    }
                }
// 用合并后的分数做分段统计
                List<BigDecimal> scores = mergedScores.stream()
                        .map(obj -> obj.getBigDecimal("scored"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());


                // 统计分数段
                int failCount = 0, count100 = 0, count90_99 = 0, count80_89 = 0, count70_79 = 0, count60_69 = 0, count50_59 = 0, count40_49 = 0, countBelow40 = 0;
                BigDecimal minScore = null;

                for (BigDecimal s : scores) {
                    if (minScore == null || s.compareTo(minScore) < 0) minScore = s;
                    if (s.compareTo(BigDecimal.valueOf(100)) == 0) count100++;
                    else if (s.compareTo(BigDecimal.valueOf(90)) >= 0) count90_99++;
                    else if (s.compareTo(BigDecimal.valueOf(80)) >= 0) count80_89++;
                    else if (s.compareTo(BigDecimal.valueOf(70)) >= 0) count70_79++;
                    else if (s.compareTo(BigDecimal.valueOf(60)) >= 0) count60_69++;
                    else if (s.compareTo(BigDecimal.valueOf(50)) >= 0) count50_59++;
                    else if (s.compareTo(BigDecimal.valueOf(40)) >= 0) count40_49++;
                    else countBelow40++;
                    if (s.compareTo(BigDecimal.valueOf(60)) < 0) failCount++;
                }
                int validCount = scores.size();
                String failRate = validCount > 0 ? new BigDecimal(failCount * 100).divide(BigDecimal.valueOf(validCount), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
                Row dataRow = sheet.createRow(table3StartRow + 3 + (i-1));
                int col = 0;
                // 班级
                Cell cell0 = dataRow.createCell(col++); cell0.setCellValue(className); cell0.setCellStyle(textStyle);
                // 在籍人数
                Cell cell1 = dataRow.createCell(col); cell1.setCellValue(enrollment); cell1.setCellStyle(integerStyle); col++;
                // 实考人数
                Cell cell2 = dataRow.createCell(col); cell2.setCellValue(validCount); cell2.setCellStyle(integerStyle); col++;
                // 总分
                Cell cell3 = dataRow.createCell(col++); cell3.setCellValue(totalScore.setScale(1, RoundingMode.HALF_UP).doubleValue()); cell3.setCellStyle(oneDecimalStyle);
                // 均分
                Cell cell4 = dataRow.createCell(col++); cell4.setCellValue(avgScore.doubleValue()); cell4.setCellStyle(decimalStyle);
                // 不及格率
                Cell cell5 = dataRow.createCell(col++); cell5.setCellValue(failRate); cell5.setCellStyle(textStyle);
                // 不及格人数
                Cell cell6 = dataRow.createCell(col); cell6.setCellValue(failCount); cell6.setCellStyle(integerStyle); col++;
                // 100
                Cell cell7 = dataRow.createCell(col); cell7.setCellValue(count100); cell7.setCellStyle(integerStyle); col++;
                // 90-99
                Cell cell8 = dataRow.createCell(col); cell8.setCellValue(count90_99); cell8.setCellStyle(integerStyle); col++;
                // 80-89
                Cell cell9 = dataRow.createCell(col); cell9.setCellValue(count80_89); cell9.setCellStyle(integerStyle); col++;
                // 70-79
                Cell cell10 = dataRow.createCell(col); cell10.setCellValue(count70_79); cell10.setCellStyle(integerStyle); col++;
                // 60-69
                Cell cell11 = dataRow.createCell(col); cell11.setCellValue(count60_69); cell11.setCellStyle(integerStyle); col++;
                // 50-59
                Cell cell12 = dataRow.createCell(col); cell12.setCellValue(count50_59); cell12.setCellStyle(integerStyle); col++;
                // 40-49
                Cell cell13 = dataRow.createCell(col); cell13.setCellValue(count40_49); cell13.setCellStyle(integerStyle); col++;
                // 40分以下
                Cell cell14 = dataRow.createCell(col); cell14.setCellValue(countBelow40); cell14.setCellStyle(integerStyle); col++;
                // 最低分
                Cell cell15 = dataRow.createCell(col++); cell15.setCellValue(minScore != null ? minScore.doubleValue() : 0); cell15.setCellStyle(decimalStyle);
                // 随班就读人数
                Cell cell16 = dataRow.createCell(col); cell16.setCellValue(enrollment); cell16.setCellStyle(integerStyle); col++;
                // 病假
                Cell cell17 = dataRow.createCell(col++); cell17.setCellValue(enrollment - validCount); cell17.setCellStyle(integerStyle);
                // 事假
                Cell cell18 = dataRow.createCell(col++); cell18.setCellValue(""); cell18.setCellStyle(textStyle);
                // 执者
                Cell cell19 = dataRow.createCell(col++); cell19.setCellValue(""); cell19.setCellStyle(textStyle);
                // 备注
                Cell cell20 = dataRow.createCell(col++); cell20.setCellValue(""); cell20.setCellStyle(textStyle);
                // 样式
                for (int j = 0; j < table3ColCount; j++) {
                    // 只为未单独设置样式的列设置默认样式
                    if (dataRow.getCell(j).getCellStyle() == null || dataRow.getCell(j).getCellStyle() == decimalStyle) {
                        dataRow.getCell(j).setCellStyle(j == 0 ? textStyle : decimalStyle);
                    }
                }
                // 年级汇总累加
                totalEnrollment += enrollment;
                totalActual += validCount;
                totalFail += failCount;
                total100 += count100;
                total90_99 += count90_99;
                total80_89 += count80_89;
                total70_79 += count70_79;
                total60_69 += count60_69;
                total50_59 += count50_59;
                total40_49 += count40_49;
                totalBelow40 += countBelow40;
                sumAvgScore = sumAvgScore.add(avgScore);
                // failRate为字符串百分比，去掉%转为BigDecimal
                try {
                    sumFailRate = sumFailRate.add(new BigDecimal(failRate.replace("%", "")));
                } catch (Exception ignore) {}
                if (minScore != null && (minOfMinScore == null || minScore.compareTo(minOfMinScore) < 0)) {
                    minOfMinScore = minScore;
                }
                totalSumScore = totalSumScore.add(totalScore); // 累加总分
                classCount++;
            }

            // 年级汇总行
            Row summaryRow = sheet.createRow(table3StartRow + 3 + classCount);
            int scol = 0;
            summaryRow.createCell(scol++).setCellValue("年级汇总");
            summaryRow.createCell(scol++).setCellValue(totalEnrollment);
            summaryRow.createCell(scol++).setCellValue(totalActual);
            Cell summaryTotalScoreCell = summaryRow.createCell(scol++);
            summaryTotalScoreCell.setCellValue(totalSumScore.setScale(1, RoundingMode.HALF_UP).doubleValue());
            summaryTotalScoreCell.setCellStyle(oneDecimalStyle);
            summaryRow.createCell(scol++).setCellValue(classCount > 0 ? sumAvgScore.divide(BigDecimal.valueOf(classCount), 1, RoundingMode.HALF_UP).doubleValue() : 0);
            summaryRow.createCell(scol++).setCellValue(classCount > 0 ? sumFailRate.divide(BigDecimal.valueOf(classCount), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%");
            summaryRow.createCell(scol++).setCellValue(totalFail);
            summaryRow.createCell(scol++).setCellValue(total100);
            summaryRow.createCell(scol++).setCellValue(total90_99);
            summaryRow.createCell(scol++).setCellValue(total80_89);
            summaryRow.createCell(scol++).setCellValue(total70_79);
            summaryRow.createCell(scol++).setCellValue(total60_69);
            summaryRow.createCell(scol++).setCellValue(total50_59);
            summaryRow.createCell(scol++).setCellValue(total40_49);
            summaryRow.createCell(scol++).setCellValue(totalBelow40);
            summaryRow.createCell(scol++).setCellValue(minOfMinScore != null ? minOfMinScore.doubleValue() : 0);
            summaryRow.createCell(scol++).setCellValue(totalEnrollment);
            summaryRow.createCell(scol++).setCellValue(totalEnrollment - totalActual);
            summaryRow.createCell(scol++).setCellValue("");
            summaryRow.createCell(scol++).setCellValue("");
            summaryRow.createCell(scol++).setCellValue("");
            summaryRow.createCell(scol++).setCellValue("");
            // 样式
            for (int j = 0; j < table3ColCount; j++) {
                if (summaryRow.getCell(j) == null) summaryRow.createCell(j);
                if (j == 0) summaryRow.getCell(j).setCellStyle(textStyle);
                else if (j == 4) summaryRow.getCell(j).setCellStyle(decimalStyle);
                else if (j == 5) summaryRow.getCell(j).setCellStyle(textStyle);
                else if (j == 15) summaryRow.getCell(j).setCellStyle(decimalStyle);
                else if (j == 16) summaryRow.getCell(j).setCellStyle(integerStyle);
                else summaryRow.getCell(j).setCellStyle(integerStyle);
            }

            // 纵向合并列索引（去掉15-最低分，避免与横向合并冲突）
            int[] mergeCols = {0,1,2,3,4,5,6,16,17,18,19,20};
            for (int colIdx : mergeCols) {
                sheet.addMergedRegion(new CellRangeAddress(table3StartRow + 1, table3StartRow + 2, colIdx, colIdx));
                // 在分段统计行写入表头内容
                table3SegmentRow.getCell(colIdx).setCellValue(table3Headers[colIdx]);
                // 表头行留空
                table3HeaderRow.getCell(colIdx).setCellValue("");
            }

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            String filename = IdUtil.fastSimpleUUID() + ".xlsx";
            return fileService.saveFile(out.toByteArray(), filename);
        } catch (Exception e) {
            throw new RuntimeException("生成Excel失败", e);
        }
    }

    /**
     * 计算第一个表格的年级汇总行
     * @param classStatisticData 班级统计数据
     * @return 年级汇总行数据
     */
    private JSONArray calculateGradeSummaryRow(JSONArray classStatisticData) {
        JSONArray gradeSummaryRow = new JSONArray();
        
        // 班级列
        gradeSummaryRow.add("年级汇总");
        
        // 初始化汇总数据
        BigDecimal totalScore = BigDecimal.ZERO;
        int totalYingkao = 0;
        int totalShikao = 0;
        int totalYouxiu = 0;
        int totalLianghao = 0;
        int totalHege = 0;
        int totalDaihege = 0;
        
        // 计算各列的总和
        for (int i = 1; i < classStatisticData.size(); i++) {
            JSONArray row = classStatisticData.getJSONArray(i);
            
            // 总分
            try {
                BigDecimal score = new BigDecimal(row.getStr(1));
                totalScore = totalScore.add(score);
            } catch (Exception e) {
                // 忽略无效数据
            }
            
            // 应考人数
            try {
                int yingkao = Integer.parseInt(row.getStr(3));
                totalYingkao += yingkao;
            } catch (Exception e) {
                // 忽略无效数据
            }
            
            // 实考人数
            try {
                int shikao = Integer.parseInt(row.getStr(4));
                totalShikao += shikao;
            } catch (Exception e) {
                // 忽略无效数据
            }
            
            // 优秀人数
            try {
                int youxiu = Integer.parseInt(row.getStr(5));
                totalYouxiu += youxiu;
            } catch (Exception e) {
                // 忽略无效数据
            }
            
            // 良好人数
            try {
                int lianghao = Integer.parseInt(row.getStr(7));
                totalLianghao += lianghao;
            } catch (Exception e) {
                // 忽略无效数据
            }
            
            // 合格人数
            try {
                int hege = Integer.parseInt(row.getStr(9));
                totalHege += hege;
            } catch (Exception e) {
                // 忽略无效数据
            }
            
            // 待合格人数
            try {
                int daihege = Integer.parseInt(row.getStr(11));
                totalDaihege += daihege;
            } catch (Exception e) {
                // 忽略无效数据
            }
        }
        
        // 计算平均分
        BigDecimal avgScore = totalShikao > 0 ? totalScore.divide(BigDecimal.valueOf(totalShikao), 1, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        
        // 计算百分比
        String youxiuPercent = totalShikao > 0 ? new BigDecimal(totalYouxiu * 100).divide(BigDecimal.valueOf(totalShikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
        String lianghaoPercent = totalShikao > 0 ? new BigDecimal(totalLianghao * 100).divide(BigDecimal.valueOf(totalShikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
        String hegePercent = totalShikao > 0 ? new BigDecimal(totalHege * 100).divide(BigDecimal.valueOf(totalShikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
        String daihegePercent = totalShikao > 0 ? new BigDecimal(totalDaihege * 100).divide(BigDecimal.valueOf(totalShikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
        
        // 构建年级汇总行
        gradeSummaryRow.add(totalScore.setScale(1, RoundingMode.HALF_UP));
        gradeSummaryRow.add(avgScore);
        gradeSummaryRow.add(totalYingkao);
        gradeSummaryRow.add(totalShikao);
        gradeSummaryRow.add(totalYouxiu);
        gradeSummaryRow.add(youxiuPercent);
        gradeSummaryRow.add(totalLianghao);
        gradeSummaryRow.add(lianghaoPercent);
        gradeSummaryRow.add(totalHege);
        gradeSummaryRow.add(hegePercent);
        gradeSummaryRow.add(totalDaihege);
        gradeSummaryRow.add(daihegePercent);
        gradeSummaryRow.add("无");
        
        return gradeSummaryRow;
    }

    /**
     * 计算第二个表格的年级汇总行
     * @param scoreRateData 得分率数据
     * @return 年级汇总行数据
     */
    private JSONArray calculateScoreRateGradeSummaryRow(JSONArray scoreRateData) {
        JSONArray gradeSummaryRow = new JSONArray();
        
        // 班级列
        gradeSummaryRow.add("年级汇总");
        
        // 计算各科目总分和得分率的汇总
        int dataRowCount = scoreRateData.size() - 1; // 减去表头行
        if (dataRowCount <= 0) {
            // 如果没有数据行，填充空值
            for (int i = 1; i < scoreRateData.getJSONArray(0).size(); i++) {
                gradeSummaryRow.add("");
            }
            return gradeSummaryRow;
        }
        
        // 计算每列的平均值（从第2列开始，第1列是班级名）
        // 现在每两列代表一个科目：第2-3列是第1个科目，第4-5列是第2个科目，以此类推
        for (int col = 1; col < scoreRateData.getJSONArray(0).size(); col++) {
            if (col % 2 == 1) {
                // 奇数列是总分列，计算总和
                BigDecimal sum = BigDecimal.ZERO;
                int validCount = 0;
                
                for (int row = 1; row < scoreRateData.size(); row++) {
                    String value = scoreRateData.getJSONArray(row).getStr(col);
                    if (StrUtil.isNotBlank(value)) {
                        try {
                            BigDecimal total = new BigDecimal(value);
                            sum = sum.add(total);
                            validCount++;
                        } catch (Exception e) {
                            // 忽略无效数据
                        }
                    }
                }
                
                if (validCount > 0) {
                    // 总分列显示总和，保留两位小数
                    gradeSummaryRow.add(sum.setScale(1, RoundingMode.HALF_UP));
                } else {
                    gradeSummaryRow.add("");
                }
            } else {
                // 偶数列是得分率列，计算平均值
                BigDecimal sum = BigDecimal.ZERO;
                int validCount = 0;
                
                for (int row = 1; row < scoreRateData.size(); row++) {
                    String value = scoreRateData.getJSONArray(row).getStr(col);
                    if (StrUtil.isNotBlank(value) && value.endsWith("%")) {
                        try {
                            String numStr = value.substring(0, value.length() - 1);
                            BigDecimal rate = new BigDecimal(numStr);
                            sum = sum.add(rate);
                            validCount++;
                        } catch (Exception e) {
                            // 忽略无效数据
                        }
                    }
                }
                
                if (validCount > 0) {
                    BigDecimal avgRate = sum.divide(BigDecimal.valueOf(validCount), 1, RoundingMode.HALF_UP);
                    gradeSummaryRow.add(avgRate.toPlainString() + "%");
                } else {
                    gradeSummaryRow.add("");
                }
            }
        }
        
        return gradeSummaryRow;
    }

    /**
     * 计算各科目得分率数据
     * @param classStatisticData 班级统计数据
     * @param req 请求参数，包含fileId信息
     * @return 各科目得分率数据
     */
    private JSONArray calculateScoreRateData(JSONArray classStatisticData, ClassStatisticDataReq req) {
        JSONArray scoreRateData = new JSONArray();

        // 如果req为空或fileDetails为空，尝试从classStatisticData中提取科目信息
        if (req == null || req.getFileDetails() == null || req.getFileDetails().isEmpty()) {
            // 从classStatisticData中分析科目类型
            Set<String> allScoreTypes = extractScoreTypesFromData(classStatisticData);
            
            // 构建表头
            JSONArray header = new JSONArray();
            header.add("班级");
            
            // 将科目类型转换为列表，最多取前5个（确保总共6列：班级+5个科目）
            List<String> scoreTypeList = new ArrayList<>(allScoreTypes);
            while (scoreTypeList.size() < 5) {
                scoreTypeList.add(""); // 用空字符串填充到5个
            }
            
            // 添加科目列标题（每个科目包含总分和得分率两列）
            for (int i = 0; i < 5; i++) {
                String type = scoreTypeList.get(i);
                if (StrUtil.isBlank(type)) {
                    header.add(""); // 总分列
                    header.add(""); // 得分率列
                } else {
                    header.add(type + "_总分"); // 总分列
                    header.add(type + "_得分率"); // 得分率列
                }
            }
            scoreRateData.add(header);

            // 构建空的数据行
            for (int i = 1; i < classStatisticData.size(); i++) {
                JSONArray row = classStatisticData.getJSONArray(i);
                String className = row.getStr(0);

                JSONArray classScoreRateRow = new JSONArray();
                classScoreRateRow.add(className);
                // 每个科目两列：总分和得分率
                for (int j = 0; j < 10; j++) { // 5个科目 * 2列 = 10列
                    classScoreRateRow.add("");
                }
                scoreRateData.add(classScoreRateRow);
            }

            return scoreRateData;
        }

        Set<String> allScoreTypes = new LinkedHashSet<>();

        // 第一步：从学生成绩数据中收集所有科目类型（参考样式四的实现）
        for (ClassStatisticDataItem item : req.getFileDetails()) {
            if (item.getFileId() == null) {
                continue;
            }

            List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(
                Wrappers.lambdaQuery(DocCorrectTask.class)
                    .eq(DocCorrectTask::getDeleted, 0)
                    .eq(DocCorrectTask::getFileId, item.getFileId())
            );

            // 收集该班级所有学生的成绩数据
            for (DocCorrectTask task : docCorrectTasks) {
                List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(task.getId(), null);
                
                if (records == null || records.isEmpty()) {
                    continue;
                }

                DocCorrectConfig config = docCorrectConfigService.getById(task.getConfigId());
                if (config == null) {
                    continue;
                }

                for (DocCorrectRecord record : records) {
                    DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
                    dto.setConfig(BeanUtil.copyProperties(config, DocCorrectConfigDTO.class));

                    JSONObject score = dto.getScore();
                    
                    if (score != null) {
                        JSONObject scoreTypeMap = score.getJSONObject("scoreTypeMap");
                        
                        if (scoreTypeMap != null) {
                            // 排除"总分"，只收集具体科目类型
                            for (String type : scoreTypeMap.keySet()) {
                                if (!"总分".equals(type)) {
                                    allScoreTypes.add(type);
                                }
                            }
                        }
                    }
                }
            }
        }

        // 如果仍然没有收集到科目类型，尝试从classStatisticData中提取
        if (allScoreTypes.isEmpty()) {
            allScoreTypes = extractScoreTypesFromData(classStatisticData);
        }

        // 第二步：构建表头（班级 + 题型数*2列）
        JSONArray header = new JSONArray();
        header.add("班级");
        List<String> scoreTypeList = new ArrayList<>(allScoreTypes);
        // 不再补齐到5个，直接用实际题型数量
        for (String type : scoreTypeList) {
            header.add(type + "_总分");
            header.add(type + "_得分率");
        }
        scoreRateData.add(header);

        // 第三步：计算每个班级的各科目总分和得分率
        for (ClassStatisticDataItem item : req.getFileDetails()) {
            JSONArray classScoreRateRow = new JSONArray();
            classScoreRateRow.add(item.getClassName());

            // 计算该班级各科目总分和得分率
            Map<String, BigDecimal> typeTotalScores = new HashMap<>();
            Map<String, BigDecimal> typeScoredScores = new HashMap<>();

            if (item.getFileId() != null) {
                List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(
                    Wrappers.lambdaQuery(DocCorrectTask.class)
                        .eq(DocCorrectTask::getDeleted, 0)
                        .eq(DocCorrectTask::getFileId, item.getFileId())
                );

                for (DocCorrectTask task : docCorrectTasks) {
                    List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(task.getId(), null);
                    if (records == null || records.isEmpty()) {
                        continue;
                    }

                    DocCorrectConfig config = docCorrectConfigService.getById(task.getConfigId());
                    if (config == null) {
                        continue;
                    }

                    for (DocCorrectRecord record : records) {
                        DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
                        dto.setConfig(BeanUtil.copyProperties(config, DocCorrectConfigDTO.class));

                        JSONObject score = dto.getScore();
                        if (score != null) {
                            JSONObject scoreTypeMap = score.getJSONObject("scoreTypeMap");
                            if (scoreTypeMap != null) {
                                for (String type : scoreTypeList) {
                                    // 排除"总分"，只处理具体科目
                                    if (!"总分".equals(type) && scoreTypeMap.containsKey(type)) {
                                        JSONObject typeObj = scoreTypeMap.getJSONObject(type);
                                        BigDecimal total = typeObj.getBigDecimal("total", BigDecimal.ZERO);
                                        BigDecimal scored = typeObj.getBigDecimal("scored", BigDecimal.ZERO);

                                        typeTotalScores.merge(type, total, BigDecimal::add);
                                        typeScoredScores.merge(type, scored, BigDecimal::add);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 只为实际题型填充数据（每个题型两列：总分和得分率）
            for (String type : scoreTypeList) {
                if (StrUtil.isBlank(type)) {
                    classScoreRateRow.add("");
                    classScoreRateRow.add("");
                } else {
                    BigDecimal total = typeTotalScores.getOrDefault(type, BigDecimal.ZERO);
                    BigDecimal scored = typeScoredScores.getOrDefault(type, BigDecimal.ZERO);
                    // 日志输出：班级名、科目名、满分总分、实际得分总分、得分率
                    log.info("[得分率统计] 班级: {}, 题型: {}, 满分总分: {}, 实际得分总分: {}, 得分率: {}%", item.getClassName(), type, total, scored, scored.divide(total, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP).toPlainString());
                    classScoreRateRow.add(scored.setScale(1, RoundingMode.HALF_UP)); // 这里改为scored
                    BigDecimal rate = BigDecimal.ZERO;
                    if (total.compareTo(BigDecimal.ZERO) > 0) {
                        rate = scored.divide(total, 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100))
                                .setScale(1, RoundingMode.HALF_UP);
                    }
                    String rateStr = rate.toPlainString() + "%";
                    classScoreRateRow.add(rateStr);
                }
            }
            scoreRateData.add(classScoreRateRow);
        }

        return scoreRateData;
    }

    /**
     * 从classStatisticData中提取科目类型
     * @param classStatisticData 班级统计数据
     * @return 科目类型集合
     */
    private Set<String> extractScoreTypesFromData(JSONArray classStatisticData) {
        Set<String> scoreTypes = new LinkedHashSet<>();
        
        // 尝试从表头中提取科目信息
        if (classStatisticData != null && !classStatisticData.isEmpty()) {
            JSONArray header = classStatisticData.getJSONArray(0);
            if (header != null && header.size() > 0) {
                // 分析表头，寻找可能的科目名称
                for (int i = 0; i < header.size(); i++) {
                    String headerName = header.getStr(i);
                    if (StrUtil.isNotBlank(headerName) && 
                        !headerName.equals("班级") && 
                        !headerName.equals("总分") && 
                        !headerName.equals("平均分") && 
                        !headerName.equals("应考人数") && 
                        !headerName.equals("实考人数") && 
                        !headerName.equals("优秀人数") && 
                        !headerName.equals("优秀占比") && 
                        !headerName.equals("良好人数") && 
                        !headerName.equals("良好占比") && 
                        !headerName.equals("合格人数") && 
                        !headerName.equals("合格占比") && 
                        !headerName.equals("待合格人数") && 
                        !headerName.equals("待合格占比") && 
                        !headerName.equals("执教老师")) {
                        scoreTypes.add(headerName);
                    }
                }
            }
        }
        
        return scoreTypes;
    }

    @Override
    public JSONArray getClassStatisticDataWithoutRangesOnlyFirstSheet(ClassStatisticDataReq req) {
        JSONArray classStatisticData = new JSONArray();
        // 新表头
        JSONArray header = new JSONArray();
        header.add("班级");
        header.add("总分");
        header.add("平均分");
        header.add("应考人数");
        header.add("实考人数");
        header.add("优秀人数");
        header.add("优秀占比");
        header.add("良好人数");
        header.add("良好占比");
        header.add("合格人数");
        header.add("合格占比");
        header.add("待合格人数");
        header.add("待合格占比");
        header.add("执教老师");
        classStatisticData.add(header);

        for (int i = 0; i < req.getFileDetails().size(); i++) {
            ClassStatisticDataItem classStatisticDataItem = req.getFileDetails().get(i);
            List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                    .eq(DocCorrectTask::getDeleted, 0)
                    .eq(DocCorrectTask::getFileId, classStatisticDataItem.getFileId()));

            String teacher = classStatisticDataItem.getTeacherName();
            if (teacher == null || teacher.trim().isEmpty()) {
                teacher = "无";
            }

            String className = classStatisticDataItem.getClassName();
            java.util.regex.Matcher matcher = java.util.regex.Pattern.compile("\\d{3}").matcher(className);
            String formattedClassName = className;
            while (matcher.find()) {
                formattedClassName = matcher.group();
            }

            JSONArray studentScores = new JSONArray();
            BigDecimal totalScore = BigDecimal.ZERO;

            // 处理多任务的情况，合并每个学生的所有任务分数
            if (!docCorrectTasks.isEmpty()) {
                // 第一次遍历：收集所有任务的记录和科目类型
                List<List<DocCorrectRecordDTO>> recordsList = new ArrayList<>();
                LinkedHashSet<String> globalTypeOrder = new LinkedHashSet<>();
                
                for (DocCorrectTask docCorrectTask : docCorrectTasks) {
                    DocCorrectConfig config = docCorrectConfigService.getById(docCorrectTask.getConfigId());
                    List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(docCorrectTask.getId(), null);
                    List<DocCorrectRecordDTO> recordDTOS = new ArrayList<>();
                    
                    for (DocCorrectRecord record : records) {
                        DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, com.chaty.dto.DocCorrectRecordDTO.class);
                        dto.setConfig(BeanUtil.copyProperties(config, com.chaty.dto.DocCorrectConfigDTO.class));
                        recordDTOS.add(dto);
                        
                        // 收集科目类型
                        JSONObject scoreTypeMap = dto.getScore().getJSONObject("scoreTypeMap");
                        if (scoreTypeMap != null) {
                            globalTypeOrder.addAll(scoreTypeMap.keySet());
                        }
                    }
                    recordsList.add(recordDTOS);
                }

                // 第二次遍历：合并每个学生的所有任务分数
                int studentCount = recordsList.get(0).size();
                for (List<DocCorrectRecordDTO> recordDTOS : recordsList) {
                    int idx = 0;
                    for (DocCorrectRecordDTO docCorrectRecordDTO : recordDTOS) {
                        JSONObject score = docCorrectRecordDTO.getScore();
                        if (studentScores.size() < studentCount) {
                            studentScores.put(score);
                        } else {
                            studentScores.put(idx, docCorrectRecordDTO.mergeScore(score, studentScores.getJSONObject(idx)));
                        }
                        idx++;
                    }
                }
            }

            int yingkao = studentScores.size();
            int shikao = studentScores.size();

            // 将优秀分、良好分、合格分写死为90、75、60
            BigDecimal youxiuScore = BigDecimal.valueOf(90);
            BigDecimal lianghaoScore = BigDecimal.valueOf(75);
            BigDecimal hegeScore = BigDecimal.valueOf(60);

            int youxiu = 0, lianghao = 0, hege = 0, daihege = 0;
            for (int j = 0; j < studentScores.size(); j++) {
                JSONObject scoreObj = studentScores.getJSONObject(j);
                BigDecimal score = scoreObj.getBigDecimal("scored");
                totalScore = totalScore.add(score);
                if (score.compareTo(youxiuScore) >= 0) {
                    youxiu++;
                } else if (score.compareTo(lianghaoScore) >= 0) {
                    lianghao++;
                } else if (score.compareTo(hegeScore) >= 0) {
                    hege++;
                } else {
                    daihege++;
                }
            }

            // 平均分保留两位小数
            BigDecimal avgScore = shikao > 0 ? totalScore.divide(BigDecimal.valueOf(shikao), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            // 比率保留一位小数
            String youxiuPercent = shikao > 0 ? new BigDecimal(youxiu * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
            String lianghaoPercent = shikao > 0 ? new BigDecimal(lianghao * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
            String hegePercent = shikao > 0 ? new BigDecimal(hege * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
            String daihegePercent = shikao > 0 ? new BigDecimal(daihege * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";

            JSONArray row = new JSONArray();
            row.add(formattedClassName);
            row.add(totalScore.setScale(1, RoundingMode.HALF_UP));
            row.add(avgScore.setScale(1, RoundingMode.HALF_UP));
            row.add(yingkao); // 整数
            row.add(shikao);  // 整数
            row.add(youxiu);  // 整数
            row.add(youxiuPercent); // 两位小数
            row.add(lianghao); // 整数
            row.add(lianghaoPercent); // 两位小数
            row.add(hege); // 整数
            row.add(hegePercent); // 两位小数
            row.add(daihege); // 整数
            row.add(daihegePercent); // 两位小数
            row.add(teacher);
            classStatisticData.add(row);
        }
        return classStatisticData;
    }

    @Override
    public JSONArray getClassStatisticDataForStyle6(ClassStatisticDataReq req) {
        JSONArray classStatisticData = new JSONArray();
        // 新表头
        JSONArray header = new JSONArray();
        header.add("班级");
        header.add("总分");
        header.add("平均分");
        header.add("应考人数");
        header.add("实考人数");
        header.add("优秀人数");
        header.add("优秀占比");
        header.add("良好人数");
        header.add("良好占比");
        header.add("合格人数");
        header.add("合格占比");
        header.add("待合格人数");
        header.add("待合格占比");
        header.add("执教老师");
        classStatisticData.add(header);

        for (int i = 0; i < req.getFileDetails().size(); i++) {
            ClassStatisticDataItem classStatisticDataItem = req.getFileDetails().get(i);
            List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                    .eq(DocCorrectTask::getDeleted, 0)
                    .eq(DocCorrectTask::getFileId, classStatisticDataItem.getFileId()));

            String teacher = classStatisticDataItem.getTeacherName();
            if (teacher == null || teacher.trim().isEmpty()) {
                teacher = "无";
            }

            String className = classStatisticDataItem.getClassName();
            java.util.regex.Matcher matcher = java.util.regex.Pattern.compile("\\d{3}").matcher(className);
            String formattedClassName = className;
            while (matcher.find()) {
                formattedClassName = matcher.group();
            }

            JSONArray studentScores = new JSONArray();
            BigDecimal totalScore = BigDecimal.ZERO;

            // 处理多任务的情况，合并每个学生的所有任务分数
            if (!docCorrectTasks.isEmpty()) {
                // 第一次遍历：收集所有任务的记录和科目类型
                List<List<DocCorrectRecordDTO>> recordsList = new ArrayList<>();
                LinkedHashSet<String> globalTypeOrder = new LinkedHashSet<>();
                
                for (DocCorrectTask docCorrectTask : docCorrectTasks) {
                    DocCorrectConfig config = docCorrectConfigService.getById(docCorrectTask.getConfigId());
                    List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(docCorrectTask.getId(), null);
                    List<DocCorrectRecordDTO> recordDTOS = new ArrayList<>();
                    
                    for (DocCorrectRecord record : records) {
                        DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, com.chaty.dto.DocCorrectRecordDTO.class);
                        dto.setConfig(BeanUtil.copyProperties(config, com.chaty.dto.DocCorrectConfigDTO.class));
                        recordDTOS.add(dto);
                        
                        // 收集科目类型
                        JSONObject scoreTypeMap = dto.getScore().getJSONObject("scoreTypeMap");
                        if (scoreTypeMap != null) {
                            globalTypeOrder.addAll(scoreTypeMap.keySet());
                        }
                    }
                    recordsList.add(recordDTOS);
                }

                // 第二次遍历：合并每个学生的所有任务分数
                int studentCount = recordsList.get(0).size();
                for (List<DocCorrectRecordDTO> recordDTOS : recordsList) {
                    int idx = 0;
                    for (DocCorrectRecordDTO docCorrectRecordDTO : recordDTOS) {
                        JSONObject score = docCorrectRecordDTO.getScore();
                        if (studentScores.size() < studentCount) {
                            studentScores.put(score);
                        } else {
                            studentScores.put(idx, docCorrectRecordDTO.mergeScore(score, studentScores.getJSONObject(idx)));
                        }
                        idx++;
                    }
                }
            }

            int yingkao = studentScores.size();
            int shikao = studentScores.size();

            // 将优秀分、良好分、合格分写死为90、75、60
            BigDecimal youxiuScore = BigDecimal.valueOf(90);
            BigDecimal lianghaoScore = BigDecimal.valueOf(75);
            BigDecimal hegeScore = BigDecimal.valueOf(60);

            int youxiu = 0, lianghao = 0, hege = 0, daihege = 0;
            for (int j = 0; j < studentScores.size(); j++) {
                JSONObject scoreObj = studentScores.getJSONObject(j);
                BigDecimal score = scoreObj.getBigDecimal("scored");
                totalScore = totalScore.add(score);
                if (score.compareTo(youxiuScore) >= 0) {
                    youxiu++;
                } else if (score.compareTo(lianghaoScore) >= 0) {
                    lianghao++;
                } else if (score.compareTo(hegeScore) >= 0) {
                    hege++;
                } else {
                    daihege++;
                }
            }

            // 平均分保留两位小数
            BigDecimal avgScore = shikao > 0 ? totalScore.divide(BigDecimal.valueOf(shikao), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            // 比率保留一位小数
            String youxiuPercent = shikao > 0 ? new BigDecimal(youxiu * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
            String lianghaoPercent = shikao > 0 ? new BigDecimal(lianghao * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
            String hegePercent = shikao > 0 ? new BigDecimal(hege * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
            String daihegePercent = shikao > 0 ? new BigDecimal(daihege * 100).divide(BigDecimal.valueOf(shikao), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";

            JSONArray row = new JSONArray();
            row.add(formattedClassName);
            row.add(totalScore.setScale(1, RoundingMode.HALF_UP));
            row.add(avgScore.setScale(1, RoundingMode.HALF_UP));
            row.add(yingkao); // 整数
            row.add(shikao);  // 整数
            row.add(youxiu);  // 整数
            row.add(youxiuPercent); // 两位小数
            row.add(lianghao); // 整数
            row.add(lianghaoPercent); // 两位小数
            row.add(hege); // 整数
            row.add(hegePercent); // 两位小数
            row.add(daihege); // 整数
            row.add(daihegePercent); // 两位小数
            row.add(teacher);
            classStatisticData.add(row);
        }
        return classStatisticData;
    }

    @Override
    public String generateClassStatisticExcel6(JSONArray classStatisticData, ClassStatisticDataReq req) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("成绩统计一览表");
            
            // 通用边框样式（细黑边框）
            BorderStyle thinBorder = BorderStyle.THIN;
            short black = IndexedColors.BLACK.getIndex();

            // 标题样式（居中、加粗、微软雅黑、字号18）
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setFontName("微软雅黑");
            titleFont.setFontHeightInPoints((short)18);
            titleFont.setColor(IndexedColors.BLACK.getIndex());
            titleFont.setBold(true);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 表头样式（左对齐）
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setColor(IndexedColors.BLACK.getIndex());
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.LEFT);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setBorderTop(thinBorder);
            headerStyle.setBorderBottom(thinBorder);
            headerStyle.setBorderLeft(thinBorder);
            headerStyle.setBorderRight(thinBorder);
            headerStyle.setTopBorderColor(black);
            headerStyle.setBottomBorderColor(black);
            headerStyle.setLeftBorderColor(black);
            headerStyle.setRightBorderColor(black);

            // 百分比样式
            DataFormat fmt = workbook.createDataFormat();
            CellStyle percentStyle = workbook.createCellStyle();
            percentStyle.setDataFormat(fmt.getFormat("0.0%"));
            percentStyle.setAlignment(HorizontalAlignment.LEFT);
            percentStyle.setBorderTop(thinBorder);
            percentStyle.setBorderBottom(thinBorder);
            percentStyle.setBorderLeft(thinBorder);
            percentStyle.setBorderRight(thinBorder);
            percentStyle.setTopBorderColor(black);
            percentStyle.setBottomBorderColor(black);
            percentStyle.setLeftBorderColor(black);
            percentStyle.setRightBorderColor(black);

            // 数值样式
            CellStyle decimalStyle = workbook.createCellStyle();
            decimalStyle.setDataFormat(fmt.getFormat("0.0"));
            decimalStyle.setAlignment(HorizontalAlignment.LEFT);
            decimalStyle.setBorderTop(thinBorder);
            decimalStyle.setBorderBottom(thinBorder);
            decimalStyle.setBorderLeft(thinBorder);
            decimalStyle.setBorderRight(thinBorder);
            decimalStyle.setTopBorderColor(black);
            decimalStyle.setBottomBorderColor(black);
            decimalStyle.setLeftBorderColor(black);
            decimalStyle.setRightBorderColor(black);

            // 整数样式
            CellStyle integerStyle = workbook.createCellStyle();
            integerStyle.setDataFormat(fmt.getFormat("0"));
            integerStyle.setAlignment(HorizontalAlignment.LEFT);
            integerStyle.setBorderTop(thinBorder);
            integerStyle.setBorderBottom(thinBorder);
            integerStyle.setBorderLeft(thinBorder);
            integerStyle.setBorderRight(thinBorder);
            integerStyle.setTopBorderColor(black);
            integerStyle.setBottomBorderColor(black);
            integerStyle.setLeftBorderColor(black);
            integerStyle.setRightBorderColor(black);

            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setAlignment(HorizontalAlignment.LEFT);
            textStyle.setBorderTop(thinBorder);
            textStyle.setBorderBottom(thinBorder);
            textStyle.setBorderLeft(thinBorder);
            textStyle.setBorderRight(thinBorder);
            textStyle.setTopBorderColor(black);
            textStyle.setBottomBorderColor(black);
            textStyle.setLeftBorderColor(black);
            textStyle.setRightBorderColor(black);

            // 表格：天宁区三年级考试相关数据统计表
            int tableStartRow = 0;

            // 标题行
            Row tableTitleRow = sheet.createRow(tableStartRow);
            tableTitleRow.setHeightInPoints(30);
            Cell tableTitleCell = tableTitleRow.createCell(0);
            tableTitleCell.setCellValue("天宁区三年级考试相关数据统计表");
            tableTitleCell.setCellStyle(titleStyle);
            int tableColCount = 18; // 学校、班级、任课老师、应考人数、实考人数、总分、均分、不及格率、不及格人数、100、90-99、80-89、70-79、60-69、50-59、40-49、40分以下、最低分
            sheet.addMergedRegion(new CellRangeAddress(tableStartRow, tableStartRow, 0, tableColCount - 1));

            // 分段统计行（在表头上方）
            Row tableSegmentRow = sheet.createRow(tableStartRow + 1);
            tableSegmentRow.setHeightInPoints(25);
            // 居中样式
            CellStyle segmentCenterStyle = workbook.createCellStyle();
            segmentCenterStyle.cloneStyleFrom(headerStyle);
            segmentCenterStyle.setAlignment(HorizontalAlignment.CENTER);
            segmentCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            for (int i = 0; i < tableColCount; i++) {
                Cell cell = tableSegmentRow.createCell(i);
                cell.setCellStyle(segmentCenterStyle);
                cell.setCellValue("");
            }
            // 合并9~17列（100~最低分）
            sheet.addMergedRegion(new CellRangeAddress(tableStartRow + 1, tableStartRow + 1, 9, 17));
            tableSegmentRow.getCell(9).setCellValue("成绩分段统计");

            // 表头行（下移一行）
            Row tableHeaderRow = sheet.createRow(tableStartRow + 2);
            tableHeaderRow.setHeightInPoints(25);
            // 为表头设置自动换行样式
            CellStyle wrapHeaderStyle = workbook.createCellStyle();
            wrapHeaderStyle.cloneStyleFrom(headerStyle);
            wrapHeaderStyle.setWrapText(true);
            
            String[] tableHeaders = new String[] {"学校", "班级", "任课老师", "应考人数", "实考人数", "总分", "均分", "不及格率", "不及格人数", "100", "90-99", "80-89", "70-79", "60-69", "50-59", "40-49", "40分以下", "最低分"};
            
            for (int i = 0; i < tableColCount; i++) {
                Cell cell = tableHeaderRow.createCell(i);
                // 设置表头内容，8为"不及格人数"
                if (i == 8) {
                    cell.setCellValue("不及格\n人数");
                    cell.setCellStyle(wrapHeaderStyle);
                } else {
                    cell.setCellValue(tableHeaders[i]);
                    cell.setCellStyle(headerStyle);
                }
                sheet.setColumnWidth(i, 10 * 256);
            }

            // 新增：表格"总分"列专用一位小数样式
            CellStyle oneDecimalStyle = workbook.createCellStyle();
            oneDecimalStyle.cloneStyleFrom(decimalStyle);
            oneDecimalStyle.setDataFormat(fmt.getFormat("0.0"));

            // 数据行（下移一行）
            JSONArray table1Data = classStatisticData; // 表1数据

            // 年级汇总统计变量
            int totalEnrollment = 0, totalActual = 0, totalFail = 0, total100 = 0, total90_99 = 0, total80_89 = 0, total70_79 = 0, total60_69 = 0, total50_59 = 0, total40_49 = 0, totalBelow40 = 0;
            BigDecimal sumAvgScore = BigDecimal.ZERO, sumFailRate = BigDecimal.ZERO;
            BigDecimal minOfMinScore = null;
            int classCount = 0;
            BigDecimal totalSumScore = BigDecimal.ZERO; // 新增：总分合计

            int maxClassRows = (req != null && req.getFileDetails() != null) ? req.getFileDetails().size() : table1Data.size() - 1;
            for (int i = 1; i < table1Data.size() && (i-1) < maxClassRows; i++) {
                JSONArray row = table1Data.getJSONArray(i);
                // 获取分数数据
                String className = row.getStr(0);
                int enrollment = row.getInt(3); // 在籍人数=应考人数
                int actual = row.getInt(4);    // 实考人数
                BigDecimal totalScore = new BigDecimal(row.getStr(1));
                BigDecimal avgScore = new BigDecimal(row.getStr(2));
                
                // 获取任课老师信息
                String teacher = "无";
                if (req != null && req.getFileDetails() != null && (i-1) < req.getFileDetails().size()) {
                    String teacherName = req.getFileDetails().get(i-1).getTeacherName();
                    if (StrUtil.isNotBlank(teacherName)) {
                        teacher = teacherName;
                    }
                }
                
                // 需要获取该班所有学生分数
                String fileId = null;
                if (req != null && req.getFileDetails() != null && (i-1) < req.getFileDetails().size()) {
                    fileId = req.getFileDetails().get(i-1).getFileId();
                }
//
                // === 表格分段统计：合并逻辑与表格1完全一致 ===
                List<List<DocCorrectRecordDTO>> recordsList = new ArrayList<>();
                int taskCount = 0, recordCount = 0;
                if (fileId != null) {
                    List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(
                            Wrappers.lambdaQuery(DocCorrectTask.class)
                                    .eq(DocCorrectTask::getDeleted, 0)
                                    .eq(DocCorrectTask::getFileId, fileId)
                    );
                    taskCount = docCorrectTasks.size();
                    for (DocCorrectTask task : docCorrectTasks) {
                        List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(task.getId(), null);
                        recordCount += records.size();
                        DocCorrectConfig config = docCorrectConfigService.getById(task.getConfigId());
                        DocCorrectConfigDTO configDTO = BeanUtil.copyProperties(config, com.chaty.dto.DocCorrectConfigDTO.class);
                        List<DocCorrectRecordDTO> recordDTOS = new ArrayList<>();
                        for (DocCorrectRecord record : records) {
                            DocCorrectRecordDTO dto = BeanUtil.copyProperties(record, com.chaty.dto.DocCorrectRecordDTO.class);
                            dto.setConfig(configDTO);
                            recordDTOS.add(dto);
                        }
                        recordsList.add(recordDTOS);
                    }
                }
// 用下标合并每个学生的所有任务分数
                List<JSONObject> mergedScores = new ArrayList<>();
                int studentCount = recordsList.isEmpty() ? 0 : recordsList.get(0).size();
                for (int idx = 0; idx < studentCount; idx++) {
                    JSONObject merged = null;
                    for (List<DocCorrectRecordDTO> recordDTOS : recordsList) {
                        if (idx >= recordDTOS.size()) continue; // 防止越界
                        DocCorrectRecordDTO dto = recordDTOS.get(idx);
                        JSONObject score = dto.getScore();
                        if (merged == null) {
                            merged = score;
                        } else {
                            merged = dto.mergeScore(score, merged);
                        }
                    }
                    if (merged != null) {
                        mergedScores.add(merged);
                    }
                }
// 用合并后的分数做分段统计
                List<BigDecimal> scores = mergedScores.stream()
                        .map(obj -> obj.getBigDecimal("scored"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 统计分数段
                int failCount = 0, count100 = 0, count90_99 = 0, count80_89 = 0, count70_79 = 0, count60_69 = 0, count50_59 = 0, count40_49 = 0, countBelow40 = 0;
                BigDecimal minScore = null;
                for (BigDecimal s : scores) {
                    if (minScore == null || s.compareTo(minScore) < 0) minScore = s;
                    if (s.compareTo(BigDecimal.valueOf(100)) == 0) count100++;
                    else if (s.compareTo(BigDecimal.valueOf(90)) >= 0) count90_99++;
                    else if (s.compareTo(BigDecimal.valueOf(80)) >= 0) count80_89++;
                    else if (s.compareTo(BigDecimal.valueOf(70)) >= 0) count70_79++;
                    else if (s.compareTo(BigDecimal.valueOf(60)) >= 0) count60_69++;
                    else if (s.compareTo(BigDecimal.valueOf(50)) >= 0) count50_59++;
                    else if (s.compareTo(BigDecimal.valueOf(40)) >= 0) count40_49++;
                    else countBelow40++;
                    if (s.compareTo(BigDecimal.valueOf(60)) < 0) failCount++;
                }
                int validCount = scores.size();
                String failRate = validCount > 0 ? new BigDecimal(failCount * 100).divide(BigDecimal.valueOf(validCount), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%";
                Row dataRow = sheet.createRow(tableStartRow + 3 + (i-1));
                int col = 0;
                // 学校
                Cell cell0 = dataRow.createCell(col++); cell0.setCellValue("清凉小学"); cell0.setCellStyle(textStyle);
                // 班级
                Cell cell1 = dataRow.createCell(col++); cell1.setCellValue(className); cell1.setCellStyle(textStyle);
                // 任课老师
                Cell cell2 = dataRow.createCell(col++); cell2.setCellValue(teacher); cell2.setCellStyle(textStyle);
                // 应考人数
                Cell cell3 = dataRow.createCell(col); cell3.setCellValue(enrollment); cell3.setCellStyle(integerStyle); col++;
                // 实考人数
                Cell cell4 = dataRow.createCell(col); cell4.setCellValue(validCount); cell4.setCellStyle(integerStyle); col++;
                // 总分
                Cell cell5 = dataRow.createCell(col++); cell5.setCellValue(totalScore.setScale(1, RoundingMode.HALF_UP).doubleValue());
                // 均分
                Cell cell6 = dataRow.createCell(col++); cell6.setCellValue(avgScore.doubleValue()); cell6.setCellStyle(decimalStyle);
                // 不及格率
                Cell cell7 = dataRow.createCell(col++); cell7.setCellValue(failRate); cell7.setCellStyle(textStyle);
                // 不及格人数
                Cell cell8 = dataRow.createCell(col); cell8.setCellValue(failCount); cell8.setCellStyle(integerStyle); col++;
                // 100
                Cell cell9 = dataRow.createCell(col); cell9.setCellValue(count100); cell9.setCellStyle(integerStyle); col++;
                // 90-99
                Cell cell10 = dataRow.createCell(col); cell10.setCellValue(count90_99); cell10.setCellStyle(integerStyle); col++;
                // 80-89
                Cell cell11 = dataRow.createCell(col); cell11.setCellValue(count80_89); cell11.setCellStyle(integerStyle); col++;
                // 70-79
                Cell cell12 = dataRow.createCell(col); cell12.setCellValue(count70_79); cell12.setCellStyle(integerStyle); col++;
                // 60-69
                Cell cell13 = dataRow.createCell(col); cell13.setCellValue(count60_69); cell13.setCellStyle(integerStyle); col++;
                // 50-59
                Cell cell14 = dataRow.createCell(col); cell14.setCellValue(count50_59); cell14.setCellStyle(integerStyle); col++;
                // 40-49
                Cell cell15 = dataRow.createCell(col); cell15.setCellValue(count40_49); cell15.setCellStyle(integerStyle); col++;
                // 40分以下
                Cell cell16 = dataRow.createCell(col); cell16.setCellValue(countBelow40); cell16.setCellStyle(integerStyle); col++;
                // 最低分
                Cell cell17 = dataRow.createCell(col++); cell17.setCellValue(minScore != null ? minScore.doubleValue() : 0); cell17.setCellStyle(decimalStyle);
                
                // 样式
                for (int j = 0; j < tableColCount; j++) {
                    // 只为未单独设置样式的列设置默认样式
                    if (dataRow.getCell(j).getCellStyle() == null || dataRow.getCell(j).getCellStyle() == decimalStyle) {
                        dataRow.getCell(j).setCellStyle(j == 0 || j == 1 || j == 2 ? textStyle : decimalStyle);
                    }
                }
                // 年级汇总累加
                totalEnrollment += enrollment;
                totalActual += validCount;
                totalFail += failCount;
                total100 += count100;
                total90_99 += count90_99;
                total80_89 += count80_89;
                total70_79 += count70_79;
                total60_69 += count60_69;
                total50_59 += count50_59;
                total40_49 += count40_49;
                totalBelow40 += countBelow40;
                sumAvgScore = sumAvgScore.add(avgScore);
                // failRate为字符串百分比，去掉%转为BigDecimal
                try {
                    sumFailRate = sumFailRate.add(new BigDecimal(failRate.replace("%", "")));
                } catch (Exception ignore) {}
                if (minScore != null && (minOfMinScore == null || minScore.compareTo(minOfMinScore) < 0)) {
                    minOfMinScore = minScore;
                }
                totalSumScore = totalSumScore.add(totalScore); // 累加总分
                classCount++;
            }

            // 年级汇总行
            Row summaryRow = sheet.createRow(tableStartRow + 3 + classCount);
            int scol = 0;
            summaryRow.createCell(scol++).setCellValue("年级汇总");
            summaryRow.createCell(scol++).setCellValue("");
            summaryRow.createCell(scol++).setCellValue("");
            summaryRow.createCell(scol++).setCellValue(totalEnrollment);
            summaryRow.createCell(scol++).setCellValue(totalActual);
            Cell summaryTotalScoreCell = summaryRow.createCell(scol++);
            summaryTotalScoreCell.setCellValue(totalSumScore.setScale(1, RoundingMode.HALF_UP).doubleValue());
            summaryTotalScoreCell.setCellStyle(oneDecimalStyle);
            summaryRow.createCell(scol++).setCellValue(classCount > 0 ? sumAvgScore.divide(BigDecimal.valueOf(classCount), 1, RoundingMode.HALF_UP).doubleValue() : 0);
            summaryRow.createCell(scol++).setCellValue(classCount > 0 ? sumFailRate.divide(BigDecimal.valueOf(classCount), 1, RoundingMode.HALF_UP).toPlainString() + "%" : "0.0%");
            summaryRow.createCell(scol++).setCellValue(totalFail);
            summaryRow.createCell(scol++).setCellValue(total100);
            summaryRow.createCell(scol++).setCellValue(total90_99);
            summaryRow.createCell(scol++).setCellValue(total80_89);
            summaryRow.createCell(scol++).setCellValue(total70_79);
            summaryRow.createCell(scol++).setCellValue(total60_69);
            summaryRow.createCell(scol++).setCellValue(total50_59);
            summaryRow.createCell(scol++).setCellValue(total40_49);
            summaryRow.createCell(scol++).setCellValue(totalBelow40);
            summaryRow.createCell(scol++).setCellValue(minOfMinScore != null ? minOfMinScore.doubleValue() : 0);
            // 样式
            for (int j = 0; j < tableColCount; j++) {
                if (summaryRow.getCell(j) == null) summaryRow.createCell(j);
                if (j == 0 || j == 1 || j == 2) summaryRow.getCell(j).setCellStyle(textStyle);
                else if (j == 6) summaryRow.getCell(j).setCellStyle(decimalStyle);
                else if (j == 7) summaryRow.getCell(j).setCellStyle(textStyle);
                else if (j == 17) summaryRow.getCell(j).setCellStyle(decimalStyle);
                else summaryRow.getCell(j).setCellStyle(integerStyle);
            }

            // 纵向合并列索引（去掉17-最低分，避免与横向合并冲突）
            int[] mergeCols = {0,1,2,3,4,5,6,7,8};
            for (int colIdx : mergeCols) {
                sheet.addMergedRegion(new CellRangeAddress(tableStartRow + 1, tableStartRow + 2, colIdx, colIdx));
                // 在分段统计行写入表头内容
                tableSegmentRow.getCell(colIdx).setCellValue(tableHeaders[colIdx]);
                // 表头行留空
                tableHeaderRow.getCell(colIdx).setCellValue("");
            }

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            String filename = IdUtil.fastSimpleUUID() + ".xlsx";
            return fileService.saveFile(out.toByteArray(), filename);
        } catch (Exception e) {
            throw new RuntimeException("生成Excel失败", e);
        }
    }

    @Override
    public List<DocCorrectTaskDTO> oneClickRetest(OneClickRetestDTO req) {
        DocCorrectFile file = docCorrectFileMapper.selectById(req.getFileId());
        if (file == null) {
            throw new RuntimeException("文件不存在");
        }
        if (req.getNewName().equals(file.getName())) {
            throw new RuntimeException("名称不能完全一致");
        }
        if (file.getStatus() != 3) {
            throw new RuntimeException("文件状态不是已批改，不能重测");
        }
        String fileId = req.getFileId();

        DocCorrectTaskDTO taskParam = new DocCorrectTaskDTO();
        taskParam.setFileId(fileId);
        PageDTO<DocCorrectTask> taskPageDTO = new PageDTO<>();
        taskPageDTO.setPageNumber(-1);
        taskPageDTO.setPageSize(9999);
        taskPageDTO.setSearchCount(false);
        taskParam.setPage(taskPageDTO);
        taskParam.setOrderByName(true);
        IPage<DocCorrectTaskDTO> docCorrectTaskDTOIPage = docCorrectTaskService.page(taskParam);
        List<DocCorrectTaskDTO> docCorrectTasks = docCorrectTaskDTOIPage.getRecords();
        List<List<DocCorrectRecord>> docCorrectRecords = new ArrayList<>();
        for (DocCorrectTask docCorrectTask : docCorrectTasks) {
            DocCorrectRecordDTO recordParam = new DocCorrectRecordDTO();
            recordParam.setTaskId(docCorrectTask.getId());
            List<DocCorrectRecord> records = docCorrectRecordService.page(recordParam).getRecords().stream()
                    .map(recordDTO -> {
                        DocCorrectRecord record = new DocCorrectRecord();
                        BeanUtil.copyProperties(recordDTO, record);
                        record.setId(IdUtil.simpleUUID());
                        return record;
                    })
                    .collect(Collectors.toList());
            docCorrectRecords.add(records);
        }
        String configPackageId = file.getConfigPackageId();
        if (StrUtil.isBlank(configPackageId)) {
            throw new RuntimeException("文件配置包id不能为空");
        }
        DocCorrectConfigPackage configPackage = docCorrectConfigPackageMapper.selectById(configPackageId);
        if (configPackage == null) {
            throw new RuntimeException("配置包不存在");
        }
        // 绑定的configs
        String configIds = configPackage.getConfig();
        List<String> configIdList = JSONUtil.toList(configIds, String.class);
        if (configIdList.isEmpty()) {
            throw new RuntimeException("配置包没有绑定配置");
        }
        List<DocCorrectConfig> configs = docCorrectConfigService.list(new QueryWrapper<DocCorrectConfig>().in("id", configIdList));
        // 重新排序,按照configIdList排序
        Map<String, Integer> orderMap = new HashMap<>(configIdList.size());
        for (int i = 0; i < configIdList.size(); i++) {
            orderMap.put(configIdList.get(i), i);
        }
        configs.sort(Comparator.comparingInt(
                cfg -> orderMap.getOrDefault(cfg.getId(), Integer.MAX_VALUE)
        ));


        JSONObject correctConfig = new JSONObject();
        correctConfig.set("id", null);
        correctConfig.set("aimodel", null);
        correctConfig.set("ocrType", 2);
        correctConfig.set("responseFormat", true);
        correctConfig.set("jsonobject", null);
        correctConfig.set("jsonschema", null);
        String correctConfigStr = JSONUtil.toJsonStr(correctConfig);
        // 开始执行复制，先 create config then config package then create file then create tasks then create records, tennatId都改为testAccountTenantId
        // —— 开始执行复制 —— //

        // 1. 复制 Config（先于 Package）
        int index = 0;
        List<String> newConfigIds = new ArrayList<>();
        String newPackageId = null;
        if (Boolean.TRUE.equals(req.getNeedNewConfig())) {
            for (DocCorrectConfig origConfig : configs) {
                index++;
                DocCorrectConfig newConfig = new DocCorrectConfig();
                BeanUtil.copyProperties(origConfig, newConfig,
                        CopyOptions.create()
                                .ignoreNullValue()
                                .ignoreError()
                                .setIgnoreProperties("id", "createTime", "updateTime")
                );
                newConfig.setId(IdUtil.simpleUUID());
                newConfig.setName(String.format("%s-配置-页(%s)", req.getNewName(), index));
                docCorrectConfigMapper.insert(newConfig);
                newConfigIds.add(newConfig.getId());
            }

            // 2. 创建新的 ConfigPackage
            DocCorrectConfigPackage newPackage = new DocCorrectConfigPackage();
            BeanUtil.copyProperties(configPackage, newPackage,
                    CopyOptions.create()
                            .ignoreNullValue()
                            .ignoreError()
                            .setIgnoreProperties("id", "createTime", "updateTime")
            );
            newPackage.setId(IdUtil.simpleUUID());
            // 使用刚复制的 newConfigIds 绑定到新包
            newPackage.setConfig(JSONUtil.toJsonStr(newConfigIds));
            newPackage.setName(req.getNewName());
            docCorrectConfigPackageMapper.insert(newPackage);
        } else {
            newConfigIds = configs.stream()
                    .map(DocCorrectConfig::getId)
                    .collect(Collectors.toList());
            newPackageId = configPackageId;
        }



        // 3. 复制 File 并关联到新 Package
        DocCorrectFile newFile = new DocCorrectFile();
        BeanUtil.copyProperties(file, newFile,
                CopyOptions.create()
                        .ignoreNullValue()
                        .ignoreError()
                        .setIgnoreProperties("id", "createTime", "updateTime")
        );
        newFile.setConfigPackageId(newPackageId);
        newFile.setId(IdUtil.simpleUUID());
        newFile.setName(req.getNewName());
        newFile.setTemplateFileId(fileId);
        newFile.setRemark("一键重测自动评估");
        docCorrectFileMapper.insert(newFile);

        // 4. 复制 Task 并关联到新 File
        List<DocCorrectTask> newTasks = new ArrayList<>();
        index = 0;
        for (DocCorrectTask origTask : docCorrectTasks) {
            DocCorrectTask newTask = new DocCorrectTask();
            BeanUtil.copyProperties(origTask, newTask,
                    CopyOptions.create()
                            .ignoreNullValue()
                            .ignoreError()
                            .setIgnoreProperties("id", "createTime", "updateTime")
            );
            newTask.setFileId(newFile.getId());
            newTask.setName(String.format("%s-页(%s)", req.getNewName(), index + 1));
            newTask.setId(IdUtil.simpleUUID());
            newTask.setConfigId(newConfigIds.get(index));
            newTask.setCorrectConfig(correctConfigStr);
            docCorrectTaskMapper.insert(newTask);
            newTasks.add(newTask);
            index++;
        }

        // 5. 复制 Record 并关联到对应的新 Task
        for (int i = 0; i < newTasks.size(); i++) {
            DocCorrectTask newTask = newTasks.get(i);
            List<DocCorrectRecord> origRecs = docCorrectRecords.get(i);
            int pageNumber = 1;
            for (DocCorrectRecord origRec : origRecs) {
                DocCorrectRecord newRec = new DocCorrectRecord();
                BeanUtil.copyProperties(origRec, newRec,
                        CopyOptions.create()
                                .ignoreNullValue()
                                .ignoreError()
                                .setIgnoreProperties("id", "createTime", "updateTime")
                );
                newRec.setTaskId(newTask.getId());
                newRec.setDocname(String.format("%s_%03d", newTask.getName(), pageNumber++));
                newRec.setId(IdUtil.simpleUUID());
                newRec.setConfigId(newTask.getConfigId());
                newRec.setFileId(newFile.getId());
                docCorrectRecordMapper.insert(newRec);
            }
        }

        // 复制record_model_setting
        if (Objects.nonNull(req.getModelSettings())) {
            req.getModelSettings().forEach(setting -> {
                setting.setFileId(newFile.getId());
                setting.setType("file");
                recordModelSettingService.save(setting);
            });
        }
        List<DocCorrectTaskDTO> res = new ArrayList<>();
        // 发起批改
        for(DocCorrectTask newTask : newTasks) {
            DocCorrectTaskDTO newTaskDTO = new DocCorrectTaskDTO();
            BeanUtil.copyProperties(newTask, newTaskDTO);
            newTaskDTO.setCorrectConfigObj(correctConfig);
            res.add(newTaskDTO);
        }
        return res;
    }

}
