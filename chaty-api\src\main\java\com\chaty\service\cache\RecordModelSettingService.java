package com.chaty.service.cache;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.RecordModelSettingEntity;

public interface RecordModelSettingService extends IService<RecordModelSettingEntity> {

    IPage<RecordModelSettingEntity> page(Integer pageNumber, Integer pageSize,
                                         String type, String fileId, String configPackageId,
                                         String questionType);

    /** 新增：写库并同步缓存 */
    Long add(RecordModelSettingEntity param);

    /** 更新：写库并同步缓存 */
    Long updateOne(RecordModelSettingEntity param);

    /** 删除：删库并同步缓存 */
    void deleteById(Long id);

    // ========= Redis 优先的便捷查询 =========
    /** 通过 fileId + questionType 获取（优先 Redis） */
    RecordModelSettingEntity getByFileAndQuestionType(String fileId, String questionType);

    /** 通过 configPackageId + questionType 获取（优先 Redis） */
    RecordModelSettingEntity getByConfigAndQuestionType(String configPackageId, String questionType);

    // ========= 手动刷新 =========
    /** 刷新某个 fileId 下的所有题型缓存 */
    void refreshCacheForFile(String fileId);

    /** 刷新某个 configPackageId 下的所有题型缓存 */
    void refreshCacheForConfig(String configPackageId);

    /** 按主键刷新缓存（根据当前库里的数据） */
    void refreshCacheById(Long id);
}
